#!/usr/bin/env python3
"""
PlexMovieAutomator/08_quality_check.py

Quality Check Script with Visual Poster Verification

This script:
- Scans folder 6 (final_plex_ready) for movies with posters
- Displays poster visually for user verification
- Provides Yes/No interface with detailed issue categorization
- YES: Keeps movie in folder 6 (final_plex_ready)
- NO: Moves movie to specific issues_hold subfolders based on problem type

Flow: Folder 6 → Visual Review + Categorized Issues → Stay in Folder 6 (approved) OR issues_hold/{category}/ (rejected)
"""

import sys
import os
import logging
import shutil
import subprocess
import time
from pathlib import Path
from datetime import datetime
from typing import List, Optional

# Fix Windows console encoding issues
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add the _internal directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('_internal/logs/quality_check.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def discover_movies_ready_for_qc():
    """
    Discover movies ready for quality check in folder 6 (final_plex_ready).
    
    Returns:
        List of movie directories with posters ready for quality check
    """
    logger.info("🔍 Discovering movies ready for quality check...")

    ready_movies = []
    workspace_path = Path("workspace")
    final_plex_ready_path = workspace_path / "6_final_plex_ready"

    if not final_plex_ready_path.exists():
        logger.warning(f"Final plex ready directory not found: {final_plex_ready_path}")
        return ready_movies

    # Check for new structure (movies/tv_shows subdirectories)
    content_type_dirs = []
    for item in final_plex_ready_path.iterdir():
        if item.is_dir():
            if item.name in ['movies', 'tv_shows']:
                # New structure: scan movies subdirectory only for this function
                if item.name == 'movies':
                    for resolution_dir in item.iterdir():
                        if resolution_dir.is_dir() and resolution_dir.name.lower() in ['1080p', '4k', '720p', '2160p']:
                            content_type_dirs.append((resolution_dir, item.name))
                            logger.debug(f"📁 Found {item.name}/{resolution_dir.name}")
            elif item.name.lower() in ['1080p', '4k', '720p', '2160p']:
                # Legacy structure: direct resolution directories (assume movies)
                content_type_dirs.append((item, 'movies'))
                logger.debug(f"📁 Found legacy {item.name}")

    if not content_type_dirs:
        logger.warning("No movie resolution directories found in final_plex_ready")
        return ready_movies

    # Process each resolution directory for movies
    for resolution_dir, content_type in content_type_dirs:
        logger.info(f"📁 Scanning {content_type}/{resolution_dir.name} content...")

        for movie_dir in resolution_dir.iterdir():
                if movie_dir.is_dir():
                    # Check if movie has both .mkv file and poster.jpg
                    mkv_files = list(movie_dir.glob("*.mkv"))
                    poster_file = movie_dir / "poster.jpg"
                    
                    if mkv_files and poster_file.exists():
                        ready_movies.append(movie_dir)
                        logger.debug(f"  ✅ Ready for QC: {movie_dir.name}")
                    else:
                        missing_items = []
                        if not mkv_files:
                            missing_items.append("movie file")
                        if not poster_file.exists():
                            missing_items.append("poster.jpg")
                        logger.debug(f"  ⚠️ Missing {', '.join(missing_items)}: {movie_dir.name}")

    logger.info(f"📊 Found {len(ready_movies)} movies ready for quality check")
    return ready_movies


def discover_tv_shows_ready_for_qc():
    """
    Discover TV shows ready for quality check in folder 6 (final_plex_ready).
    TV shows are organized by series/season structure with episode files.
    
    Returns:
        List of TV show series directories with posters ready for quality check
    """
    logger.info("📺 Discovering TV shows ready for quality check...")

    ready_tv_shows = []
    workspace_path = Path("workspace")
    final_plex_ready_path = workspace_path / "6_final_plex_ready"

    if not final_plex_ready_path.exists():
        logger.warning(f"Final plex ready directory not found: {final_plex_ready_path}")
        return ready_tv_shows

    # Check for new structure (movies/tv_shows subdirectories)
    content_type_dirs = []
    for item in final_plex_ready_path.iterdir():
        if item.is_dir():
            if item.name == 'tv_shows':
                # Check if this is consolidated structure (no resolution folders)
                # Look for series directories directly under tv_shows/
                has_direct_series = False
                has_resolution_dirs = False
                
                for subitem in item.iterdir():
                    if subitem.is_dir():
                        if subitem.name.lower() in ['1080p', '4k', '720p', '2160p']:
                            has_resolution_dirs = True
                            # New structure with resolution dirs: scan tv_shows/resolution/ subdirectory
                            content_type_dirs.append((subitem, item.name))
                            logger.debug(f"📁 Found {item.name}/{subitem.name}")
                        else:
                            # Check if this looks like a TV series (has season directories or episodes)
                            has_seasons = any(d.is_dir() and 'season' in d.name.lower() 
                                            for d in subitem.iterdir() if d.is_dir())
                            has_episodes = any(f.is_file() and f.suffix.lower() == '.mkv' 
                                             for f in subitem.rglob('*.mkv'))
                            
                            if has_seasons or has_episodes:
                                has_direct_series = True
                
                # If we found direct series but no resolution dirs, this is consolidated structure
                if has_direct_series and not has_resolution_dirs:
                    content_type_dirs.append((item, 'tv_shows_consolidated'))
                    logger.debug(f"📁 Found consolidated TV shows structure")
                    
            elif item.name.lower() in ['1080p', '4k', '720p', '2160p']:
                # Legacy structure: check for TV show patterns in resolution directories
                # Look for series directories that contain season subdirectories
                for series_candidate in item.iterdir():
                    if series_candidate.is_dir():
                        # Check if this looks like a TV series (has season directories or episodes)
                        has_seasons = any(d.is_dir() and 'season' in d.name.lower() 
                                        for d in series_candidate.iterdir())
                        has_episodes = any(f.is_file() and f.suffix.lower() == '.mkv' 
                                         for f in series_candidate.rglob('*.mkv'))
                        
                        if has_seasons or has_episodes:
                            # This looks like a TV series
                            content_type_dirs.append((item, 'tv_shows_legacy'))
                            logger.debug(f"📁 Found legacy TV shows in {item.name}")
                            break

    if not content_type_dirs:
        logger.warning("No TV show resolution directories found in final_plex_ready")
        return ready_tv_shows

    # Process each resolution directory for TV shows
    for resolution_dir, content_type in content_type_dirs:
        logger.info(f"📁 Scanning {content_type}/{resolution_dir.name} content...")

        for series_dir in resolution_dir.iterdir():
            if series_dir.is_dir():
                # Check if this is a TV series with poster and episodes
                poster_file = series_dir / "poster.jpg"
                
                # Look for episodes in the series directory (either direct or in season subdirs)
                episode_files = list(series_dir.rglob("*.mkv"))
                
                # Check for season subdirectories
                season_dirs = [d for d in series_dir.iterdir() if d.is_dir() and 
                             any(pattern in d.name.lower() for pattern in ['season', 's0', 's1', 's2', 's3', 's4', 's5', 's6', 's7', 's8', 's9'])]
                
                has_episodes = len(episode_files) > 0
                has_seasons = len(season_dirs) > 0
                
                if (has_episodes or has_seasons) and poster_file.exists():
                    ready_tv_shows.append(series_dir)
                    logger.debug(f"  ✅ Ready for TV QC: {series_dir.name} ({len(episode_files)} episodes, {len(season_dirs)} seasons)")
                else:
                    missing_items = []
                    if not has_episodes and not has_seasons:
                        missing_items.append("episodes")
                    if not poster_file.exists():
                        missing_items.append("poster.jpg")
                    logger.debug(f"  ⚠️ Missing {', '.join(missing_items)}: {series_dir.name}")

    logger.info(f"📊 Found {len(ready_tv_shows)} TV shows ready for quality check")
    return ready_tv_shows


def display_poster_image(poster_path: Path) -> bool:
    """
    Display poster image using the default image viewer.
    
    Args:
        poster_path: Path to poster.jpg file
        
    Returns:
        bool: True if successfully displayed, False otherwise
    """
    try:
        if sys.platform == "win32":
            # Use Windows default image viewer
            os.startfile(str(poster_path))
        elif sys.platform == "darwin":  # macOS
            subprocess.run(["open", str(poster_path)], check=True)
        else:  # Linux
            subprocess.run(["xdg-open", str(poster_path)], check=True)
        
        logger.info(f"🖼️ Displayed poster: {poster_path.name}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to display poster {poster_path}: {e}")
        return False


def close_image_viewers():
    """
    Attempt to close any open image viewers on Windows.
    """
    try:
        if sys.platform == "win32":
            # More comprehensive list of image viewer processes to close
            image_viewer_processes = [
                "Microsoft.Photos.exe",
                "PhotosApp.exe", 
                "WindowsPhotoViewer.exe",
                "Photos.exe",
                "photos.exe",  # Lowercase version
                "Microsoft.Windows.Photos.exe",
                "dllhost.exe",
                "Windows.Media.Viewer.exe"
            ]
            
            for process in image_viewer_processes:
                try:
                    result = subprocess.run(["taskkill", "/f", "/im", process], 
                                          capture_output=True, check=False)
                    if result.returncode == 0:
                        logger.debug(f"Closed image viewer process: {process}")
                except:
                    pass
                    
            # Also try to close any process with "photo" in the name
            try:
                subprocess.run(["taskkill", "/f", "/fi", "IMAGENAME eq *photo*"], 
                             capture_output=True, check=False)
            except:
                pass
                
    except Exception as e:
        logger.debug(f"Note: Could not close image viewers: {e}")


def get_issue_categories():
    """
    Define the available issue categories for problem classification.
    
    Returns:
        dict: Issue categories with descriptions
    """
    return {
        "poster_quality": {
            "folder": "poster_quality_issues",
            "description": "Poster quality issues (wrong poster, low resolution, poor quality)"
        },
        "subtitle_problems": {
            "folder": "subtitle_issues", 
            "description": "Subtitle problems (missing, wrong language, sync issues)"
        },
        "video_quality": {
            "folder": "video_quality_issues",
            "description": "Video quality issues (encoding problems, artifacts, wrong resolution)"
        },
        "audio_problems": {
            "folder": "audio_issues",
            "description": "Audio problems (sync issues, wrong language, quality problems)"
        },
        "file_corruption": {
            "folder": "file_corruption",
            "description": "File corruption or playback issues"
        },
        "metadata_issues": {
            "folder": "metadata_issues", 
            "description": "Metadata problems (wrong title, year, naming issues)"
        },
        "other": {
            "folder": "other_issues",
            "description": "Other unspecified issues requiring manual review"
        }
    }


def move_to_issues_hold(movie_dir: Path, issue_category: str) -> bool:
    """
    Move movie with issues to appropriate issues_hold subfolder.
    
    Args:
        movie_dir: Path to movie directory in folder 6
        issue_category: Category of the issue (key from get_issue_categories())
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        movie_name = movie_dir.name
        resolution = movie_dir.parent.name  # 1080p, 4k, etc.
        
        categories = get_issue_categories()
        if issue_category not in categories:
            issue_category = "other"
        
        folder_name = categories[issue_category]["folder"]
        
        # Create issues_hold destination
        issues_base = Path("workspace") / "issues_hold" / folder_name
        issues_dest = issues_base / resolution / movie_name
        issues_dest.mkdir(parents=True, exist_ok=True)
        
        # Move all files in the movie directory
        files_moved = 0
        for file_item in movie_dir.iterdir():
            if file_item.is_file():
                dest_file = issues_dest / file_item.name
                shutil.move(str(file_item), str(dest_file))
                logger.info(f"📁 Moved: {file_item.name} → issues_hold/{folder_name}")
                files_moved += 1
        
        # Remove the now-empty folder 6 directory
        try:
            movie_dir.rmdir()
            logger.info(f"🗑️ Removed empty folder 6 directory: {movie_name}")
        except OSError:
            remaining_files = list(movie_dir.iterdir())
            logger.warning(f"⚠️ Folder 6 directory not empty: {[f.name for f in remaining_files]}")
        
        logger.info(f"📋 Moved to issues_hold/{folder_name}: {movie_name} ({files_moved} files)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to move to issues_hold: {e}")
        return False


def move_tv_show_to_issues_hold(series_dir: Path, issue_category: str) -> bool:
    """
    Move TV show with issues to appropriate issues_hold subfolder.
    Maintains series/season structure in issues_hold.
    
    Args:
        series_dir: Path to TV series directory in folder 6
        issue_category: Category of the issue (key from get_issue_categories())
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        series_name = series_dir.name
        resolution = series_dir.parent.name  # 1080p, 4k, etc.
        
        categories = get_issue_categories()
        if issue_category not in categories:
            issue_category = "other"
        
        folder_name = categories[issue_category]["folder"]
        
        # Create issues_hold destination for TV show
        issues_base = Path("workspace") / "issues_hold" / folder_name / "tv_shows"
        issues_dest = issues_base / resolution / series_name
        issues_dest.mkdir(parents=True, exist_ok=True)
        
        # Move all content in the series directory (episodes, seasons, poster)
        items_moved = 0
        for item in series_dir.iterdir():
            dest_item = issues_dest / item.name
            if item.is_dir():
                # Move entire season directory
                shutil.move(str(item), str(dest_item))
                logger.info(f"📁 Moved season: {item.name} → issues_hold/{folder_name}/tv_shows")
                items_moved += 1
            elif item.is_file():
                # Move file (poster, individual episodes, etc.)
                shutil.move(str(item), str(dest_item))
                logger.info(f"📁 Moved file: {item.name} → issues_hold/{folder_name}/tv_shows")
                items_moved += 1
        
        # Remove the now-empty folder 6 directory
        try:
            series_dir.rmdir()
            logger.info(f"🗑️ Removed empty folder 6 directory: {series_name}")
        except OSError:
            remaining_items = list(series_dir.iterdir())
            logger.warning(f"⚠️ Folder 6 directory not empty: {[i.name for i in remaining_items]}")
        
        logger.info(f"📋 Moved TV show to issues_hold/{folder_name}/tv_shows: {series_name} ({items_moved} items)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to move TV show to issues_hold: {e}")
        return False


def interactive_quality_check(movie_dir: Path) -> bool:
    """
    Interactive quality check for a single movie with visual poster verification.
    
    Args:
        movie_dir: Path to movie directory
        
    Returns:
        bool: True if approved, False if moved to issues_hold
    """
    movie_name = movie_dir.name
    poster_file = movie_dir / "poster.jpg"
    
    logger.info(f"\n🎬 Quality Check: {movie_name}")
    print(f"\n{'='*60}")
    print(f"🎬 Quality Check: {movie_name}")
    print(f"{'='*60}")
    
    # Display poster image
    if poster_file.exists():
        print(f"🖼️ Opening poster image for visual verification...")
        if display_poster_image(poster_file):
            print(f"✅ Poster displayed. Please review the image.")
        else:
            print(f"⚠️ Could not display poster automatically.")
            print(f"📂 Please manually check: {poster_file}")
    else:
        print(f"⚠️ No poster found for this movie!")
    
    # Get user decision
    while True:
        print(f"\n🤔 Does this movie meet quality standards?")
        print(f"   📂 Movie: {movie_name}")
        print(f"   🖼️ Poster: {'✅ Present' if poster_file.exists() else '❌ Missing'}")
        print(f"\nOptions:")
        print(f"  y/yes - Approve movie (keep in final_plex_ready)")
        print(f"  n/no  - Reject movie (categorize issue and move to issues_hold)")
        print(f"  s     - Skip this movie for now")
        print(f"  q     - Quit quality check")
        
        choice = input(f"\nYour choice [y/n/s/q]: ").strip().lower()
        
        if choice in ['y', 'yes']:
            # Movie is already in final_plex_ready, just approve it
            print(f"✅ Movie approved and kept in final_plex_ready!")
            logger.info(f"✅ Approved movie: {movie_name}")
            
            # Close the poster image after approval
            close_image_viewers()
            
            # Give Windows a moment to close the image viewers
            time.sleep(0.5)
            
            return True
                
        elif choice in ['n', 'no']:
            # Close the poster image
            close_image_viewers()
            
            # Get issue category
            print(f"\n📋 What type of issue did you find?")
            categories = get_issue_categories()
            
            print(f"\nIssue Categories:")
            for i, (key, info) in enumerate(categories.items(), 1):
                print(f"  {i}. {info['description']}")
            
            while True:
                try:
                    category_choice = input(f"\nSelect issue category [1-{len(categories)}]: ").strip()
                    category_index = int(category_choice) - 1
                    
                    if 0 <= category_index < len(categories):
                        selected_category = list(categories.keys())[category_index]
                        break
                    else:
                        print(f"Please enter a number between 1 and {len(categories)}")
                except ValueError:
                    print(f"Please enter a valid number")
            
            # Move to issues_hold
            if move_to_issues_hold(movie_dir, selected_category):
                category_info = categories[selected_category]
                print(f"📋 Movie moved to issues_hold/{category_info['folder']}")
                return False
            else:
                print(f"❌ Failed to move movie to issues_hold")
                return False
                
        elif choice in ['s', 'skip']:
            close_image_viewers()
            print(f"⏭️ Skipped: {movie_name}")
            return False
            
        elif choice in ['q', 'quit']:
            close_image_viewers()
            print(f"👋 Exiting quality check")
            return False
            
        else:
            print(f"Please enter 'y', 'n', 's', or 'q'")


def interactive_tv_show_quality_check(series_dir: Path) -> bool:
    """
    Interactive quality check for a single TV show with visual poster verification.
    TV show-specific version with series/season/episode awareness.
    
    Args:
        series_dir: Path to TV series directory
        
    Returns:
        bool: True if approved, False if moved to issues_hold
    """
    series_name = series_dir.name
    poster_file = series_dir / "poster.jpg"
    
    # Get TV show statistics
    episode_files = list(series_dir.rglob("*.mkv"))
    season_dirs = [d for d in series_dir.iterdir() if d.is_dir() and 
                   any(pattern in d.name.lower() for pattern in ['season', 's0', 's1', 's2', 's3', 's4', 's5', 's6', 's7', 's8', 's9'])]
    
    logger.info(f"\n📺 TV Show Quality Check: {series_name}")
    print(f"\n{'='*60}")
    print(f"📺 TV Show Quality Check: {series_name}")
    print(f"{'='*60}")
    print(f"📊 Episodes: {len(episode_files)}")
    print(f"📊 Seasons: {len(season_dirs)}")
    
    # Display poster image
    if poster_file.exists():
        print(f"🖼️ Opening series poster for visual verification...")
        if display_poster_image(poster_file):
            print(f"✅ Series poster displayed. Please review the image.")
        else:
            print(f"⚠️ Could not display poster automatically.")
            print(f"📂 Please manually check: {poster_file}")
    else:
        print(f"⚠️ No series poster found!")
    
    # Get user decision
    while True:
        print(f"\n🤔 Does this TV show meet quality standards?")
        print(f"   📂 Series: {series_name}")
        print(f"   🖼️ Poster: {'✅ Present' if poster_file.exists() else '❌ Missing'}")
        print(f"   📊 Content: {len(episode_files)} episodes in {len(season_dirs)} seasons")
        print(f"\nOptions:")
        print(f"  y/yes - Approve TV show (keep in final_plex_ready)")
        print(f"  n/no  - Reject TV show (categorize issue and move to issues_hold)")
        print(f"  s     - Skip this TV show for now")
        print(f"  q     - Quit quality check")
        
        choice = input(f"\nYour choice [y/n/s/q]: ").strip().lower()
        
        if choice in ['y', 'yes']:
            # TV show is already in final_plex_ready, just approve it
            print(f"✅ TV show approved and kept in final_plex_ready!")
            logger.info(f"✅ Approved TV show: {series_name}")
            
            # Close the poster image after approval
            close_image_viewers()
            
            # Give Windows a moment to close the image viewers
            time.sleep(0.5)
            
            return True
                
        elif choice in ['n', 'no']:
            # Close the poster image
            close_image_viewers()
            
            # Get issue category (same categories as movies, but TV-specific handling)
            print(f"\n📋 What type of issue did you find with this TV show?")
            categories = get_issue_categories()
            
            print(f"\nIssue Categories (TV Show):")
            for i, (key, info) in enumerate(categories.items(), 1):
                # Customize descriptions for TV shows
                tv_description = info['description']
                if 'poster' in tv_description.lower():
                    tv_description = tv_description.replace('Poster', 'Series poster')
                elif 'subtitle' in tv_description.lower():
                    tv_description = tv_description.replace('Subtitle', 'Episode subtitle')
                elif 'video' in tv_description.lower():
                    tv_description = tv_description.replace('Video', 'Episode video')
                elif 'audio' in tv_description.lower():
                    tv_description = tv_description.replace('Audio', 'Episode audio')
                
                print(f"  {i}. {tv_description}")
            
            while True:
                try:
                    category_choice = input(f"\nSelect issue category [1-{len(categories)}]: ").strip()
                    category_index = int(category_choice) - 1
                    
                    if 0 <= category_index < len(categories):
                        selected_category = list(categories.keys())[category_index]
                        break
                    else:
                        print(f"Please enter a number between 1 and {len(categories)}")
                except ValueError:
                    print(f"Please enter a valid number")
            
            # Move TV show to issues_hold
            if move_tv_show_to_issues_hold(series_dir, selected_category):
                category_info = categories[selected_category]
                print(f"📋 TV show moved to issues_hold/{category_info['folder']}/tv_shows")
                return False
            else:
                print(f"❌ Failed to move TV show to issues_hold")
                return False
                
        elif choice in ['s', 'skip']:
            close_image_viewers()
            print(f"⏭️ Skipped: {series_name}")
            return False
            
        elif choice in ['q', 'quit']:
            close_image_viewers()
            print(f"👋 Exiting TV show quality check")
            return False
            
        else:
            print(f"Please enter 'y', 'n', 's', or 'q'")


def process_quality_check():
    """
    Process all movies and TV shows awaiting quality check.
    """
    logger.info("🔍 Starting Quality Check with Visual Poster Verification")
    print(f"🎨 Quality Check with Visual Poster Verification")
    print(f"=" * 60)
    
    # Discover content ready for quality check
    ready_movies = discover_movies_ready_for_qc()
    ready_tv_shows = discover_tv_shows_ready_for_qc()
    
    total_content = len(ready_movies) + len(ready_tv_shows)
    
    if total_content == 0:
        logger.info("✅ No content ready for quality check")
        print(f"✅ No movies or TV shows ready for quality check")
        return
    
    approved = 0
    rejected = 0
    skipped = 0
    
    print(f"📊 Found content ready for quality check:")
    print(f"   🎬 Movies: {len(ready_movies)}")
    print(f"   📺 TV Shows: {len(ready_tv_shows)}")
    print(f"   📊 Total: {total_content}")
    print(f"🖼️ Posters will be displayed automatically for visual verification")
    
    content_index = 0
    
    # Process movies first
    if ready_movies:
        print(f"\n{'='*60}")
        print(f"🎬 PROCESSING MOVIES ({len(ready_movies)} found)")
        print(f"{'='*60}")
        
        for movie_dir in ready_movies:
            content_index += 1
            try:
                print(f"\n📍 Progress: {content_index}/{total_content} (Movie)")
                
                result = interactive_quality_check(movie_dir)
                
                if result:
                    approved += 1
                else:
                    # Check if movie still exists (not skipped)
                    if not movie_dir.exists():
                        rejected += 1
                    else:
                        skipped += 1
                        
            except KeyboardInterrupt:
                print(f"\n👋 Quality check interrupted by user")
                close_image_viewers()
                return
            except Exception as e:
                logger.error(f"❌ Error processing movie {movie_dir.name}: {e}")
                skipped += 1
    
    # Process TV shows
    if ready_tv_shows:
        print(f"\n{'='*60}")
        print(f"📺 PROCESSING TV SHOWS ({len(ready_tv_shows)} found)")
        print(f"{'='*60}")
        
        for series_dir in ready_tv_shows:
            content_index += 1
            try:
                print(f"\n📍 Progress: {content_index}/{total_content} (TV Show)")
                
                result = interactive_tv_show_quality_check(series_dir)
                
                if result:
                    approved += 1
                else:
                    # Check if TV show still exists (not skipped)
                    if not series_dir.exists():
                        rejected += 1
                    else:
                        skipped += 1
                        
            except KeyboardInterrupt:
                print(f"\n👋 Quality check interrupted by user")
                close_image_viewers()
                return
            except Exception as e:
                logger.error(f"❌ Error processing TV show {series_dir.name}: {e}")
                skipped += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"📊 Quality Check Summary:")
    print(f"   ✅ Approved: {approved}")
    print(f"   ❌ Rejected: {rejected}")
    print(f"   ⏭️ Skipped: {skipped}")
    print(f"   📁 Total processed: {approved + rejected}")
    
    if approved > 0:
        print(f"\n🎉 {approved} items approved and kept in final_plex_ready!")
        print(f"📁 Check workspace/6_final_plex_ready/ for finalized content")
    
    if rejected > 0:
        print(f"\n📋 {rejected} items moved to issues_hold for review")
        print(f"📁 Check workspace/issues_hold/ for categorized issues")
    
    # Final cleanup - close any remaining open image viewers
    print(f"\n🔧 Closing any remaining image viewers...")
    close_image_viewers()


def process_movies_quality_check():
    """
    Process only movies awaiting quality check.
    """
    logger.info("🔍 Starting Movies Quality Check with Visual Poster Verification")
    print(f"🎬 Movies Quality Check with Visual Poster Verification")
    print(f"=" * 60)
    
    ready_movies = discover_movies_ready_for_qc()
    
    if not ready_movies:
        logger.info("✅ No movies ready for quality check")
        print(f"✅ No movies ready for quality check")
        return
    
    approved = 0
    rejected = 0
    skipped = 0
    
    print(f"📊 Found {len(ready_movies)} movies ready for quality check")
    print(f"🖼️ Posters will be displayed automatically for visual verification")
    
    for i, movie_dir in enumerate(ready_movies, 1):
        try:
            print(f"\n📍 Progress: {i}/{len(ready_movies)}")
            
            result = interactive_quality_check(movie_dir)
            
            if result:
                approved += 1
            else:
                # Check if movie still exists (not skipped)
                if not movie_dir.exists():
                    rejected += 1
                else:
                    skipped += 1
                    
        except KeyboardInterrupt:
            print(f"\n👋 Quality check interrupted by user")
            close_image_viewers()
            break
        except Exception as e:
            logger.error(f"❌ Error processing {movie_dir.name}: {e}")
            skipped += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"📊 Movies Quality Check Summary:")
    print(f"   ✅ Approved: {approved}")
    print(f"   ❌ Rejected: {rejected}")
    print(f"   ⏭️ Skipped: {skipped}")
    print(f"   📁 Total processed: {approved + rejected}")
    
    if approved > 0:
        print(f"\n🎉 {approved} movies approved and kept in final_plex_ready!")
        print(f"📁 Check workspace/6_final_plex_ready/ for finalized movies")
    
    if rejected > 0:
        print(f"\n📋 {rejected} movies moved to issues_hold for review")
        print(f"📁 Check workspace/issues_hold/ for categorized issues")
    
    # Final cleanup
    close_image_viewers()


def process_tv_shows_quality_check():
    """
    Process only TV shows awaiting quality check.
    """
    logger.info("📺 Starting TV Shows Quality Check with Visual Poster Verification")
    print(f"📺 TV Shows Quality Check with Visual Poster Verification")
    print(f"=" * 60)
    
    ready_tv_shows = discover_tv_shows_ready_for_qc()
    
    if not ready_tv_shows:
        logger.info("✅ No TV shows ready for quality check")
        print(f"✅ No TV shows ready for quality check")
        return
    
    approved = 0
    rejected = 0
    skipped = 0
    
    print(f"📊 Found {len(ready_tv_shows)} TV shows ready for quality check")
    print(f"🖼️ Series posters will be displayed automatically for visual verification")
    
    for i, series_dir in enumerate(ready_tv_shows, 1):
        try:
            print(f"\n� Progress: {i}/{len(ready_tv_shows)}")
            
            result = interactive_tv_show_quality_check(series_dir)
            
            if result:
                approved += 1
            else:
                # Check if TV show still exists (not skipped)
                if not series_dir.exists():
                    rejected += 1
                else:
                    skipped += 1
                    
        except KeyboardInterrupt:
            print(f"\n👋 TV shows quality check interrupted by user")
            close_image_viewers()
            break
        except Exception as e:
            logger.error(f"❌ Error processing TV show {series_dir.name}: {e}")
            skipped += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"📊 TV Shows Quality Check Summary:")
    print(f"   ✅ Approved: {approved}")
    print(f"   ❌ Rejected: {rejected}")
    print(f"   ⏭️ Skipped: {skipped}")
    print(f"   📁 Total processed: {approved + rejected}")
    
    if approved > 0:
        print(f"\n🎉 {approved} TV shows approved and kept in final_plex_ready!")
        print(f"📁 Check workspace/6_final_plex_ready/ for finalized TV shows")
    
    if rejected > 0:
        print(f"\n📋 {rejected} TV shows moved to issues_hold for review")
        print(f"📁 Check workspace/issues_hold/ for categorized issues")
    
    # Final cleanup
    close_image_viewers()


def display_interactive_menu():
    """
    Display the main interactive menu for content type selection.

    Returns:
        str: Selected content type ('movies', 'tv_shows', 'both', 'quit')
    """
    print(f"\n{'='*60}")
    print(f"🎬📺 PlexMovieAutomator - Interactive Content Selection")
    print(f"{'='*60}")
    print(f"\nWhat type of content would you like to process?")
    print(f"  1. Movies only")
    print(f"  2. TV Shows only")
    print(f"  3. Both Movies and TV Shows")
    print(f"  4. Quit")

    while True:
        try:
            choice = input(f"\nEnter your choice [1-4]: ").strip()

            if choice == '1':
                return 'movies'
            elif choice == '2':
                return 'tv_shows'
            elif choice == '3':
                return 'both'
            elif choice == '4':
                return 'quit'
            else:
                print(f"Please enter a number between 1 and 4")

        except KeyboardInterrupt:
            print(f"\n👋 Exiting...")
            return 'quit'


def main():
    """
    Main function for quality check supporting both movies and TV shows.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='Quality Check Script with Visual Poster Verification')
    parser.add_argument('--movies-only', action='store_true',
                       help='Process only movies (command-line mode)')
    parser.add_argument('--tv-only', action='store_true',
                       help='Process only TV shows (command-line mode)')
    parser.add_argument('--all', action='store_true',
                       help='Process both movies and TV shows (command-line mode)')
    
    args = parser.parse_args()
    
    logger.info("🎨 Starting Quality Check Script (Script 08)")
    logger.info("   Supporting Movies and TV Shows with Visual Poster Verification")
    logger.info("   Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)")
    
    try:
        # Default to interactive mode unless command-line content arguments are specified
        if args.movies_only or args.tv_only or args.all:
            # Command line mode - user specified content type arguments
            if args.movies_only:
                content_type_choice = 'movies'
            elif args.tv_only:
                content_type_choice = 'tv_shows'
            else:  # args.all
                content_type_choice = 'both'
        else:
            # Interactive mode (default behavior)
            content_type_choice = display_interactive_menu()
            
            if content_type_choice == 'quit':
                logger.info("👋 User chose to quit")
                return
        
        # Process based on selection
        if content_type_choice == 'movies':
            logger.info("🎬 Processing movies only")
            process_movies_quality_check()
        elif content_type_choice == 'tv_shows':
            logger.info("📺 Processing TV shows only")
            process_tv_shows_quality_check()
        else:
            # Process both movies and TV shows
            logger.info("🎬📺 Processing both movies and TV shows")
            process_quality_check()
        
    except KeyboardInterrupt:
        logger.info("👋 Quality check interrupted by user")
        print(f"\n👋 Quality check interrupted")
        close_image_viewers()
        
    except Exception as e:
        logger.error(f"❌ Quality check failed: {e}")
        print(f"❌ Quality check failed: {e}")
        close_image_viewers()
    
    finally:
        # Final cleanup - ensure all image viewers are closed
        close_image_viewers()


if __name__ == "__main__":
    main()
