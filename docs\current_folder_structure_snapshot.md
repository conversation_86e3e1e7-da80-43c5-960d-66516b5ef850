# Current Folder Structure Snapshot

## Folder 5 (Script 7 Input)
```
workspace/5_awaiting_poster/
├── movies/
│   ├── 4k/
│   │   └── MovieName (2024)/
│   └── 1080p/
│       └── AnotherMovie (2023)/
└── tv_shows/
    ├── Futurama (1999)/              ← CONSOLIDATED (no resolution)
    │   ├── Season 01/
    │   ├── Season 02/
    │   └── Season 03/
    └── Another Show (2020)/
        └── Season 01/
```

## Temp Original Backup (Script 7 Output Target)
```
workspace/temp_original_backup/
├── movies/
│   ├── 4k/
│   │   ├── MovieName (2024)/         ← Script 3 backup
│   │   ├── posters/                  ← Script 7 target
│   │   └── temp_analysis/            ← Script 7 working
│   └── 1080p/
│       ├── AnotherMovie (2023)/
│       ├── posters/
│       └── temp_analysis/
└── tv_shows/
    ├── 720p/
    │   ├── Futurama (1999)/          ← Script 3 backup (Season 01)
    │   ├── posters/                  ← Script 7 target options
    │   └── temp_analysis/
    ├── 1080p/
    │   ├── Futurama (1999)/          ← Script 3 backup (Season 02, 03)
    │   ├── Another Show (2020)/
    │   ├── posters/
    │   └── temp_analysis/
    └── 4k/
        ├── posters/
        └── temp_analysis/
```

## The Resolution Detection Challenge

### Movies (WORKS) ✅
```python
# Script 7 can detect resolution from folder 5 structure:
input_path = "5_awaiting_poster/movies/4k/MovieName (2024)/"
resolution = "4k"  # Detected from parent folder
target = "temp_original_backup/movies/4k/posters/MovieName (2024)/"
```

### TV Shows (PROBLEM) ❌
```python
# Script 7 cannot detect resolution from folder 5 structure:
input_path = "5_awaiting_poster/tv_shows/Futurama (1999)/"
resolution = "???"  # No resolution info in path

# Multiple possible targets exist:
# temp_original_backup/tv_shows/720p/posters/Futurama (1999)/
# temp_original_backup/tv_shows/1080p/posters/Futurama (1999)/
# temp_original_backup/tv_shows/4k/posters/Futurama (1999)/
```

## Multi-Resolution Reality Example
```
# Same show exists at different resolutions in temp_original_backup:
temp_original_backup/tv_shows/720p/Futurama (1999)/Season 01/
temp_original_backup/tv_shows/1080p/Futurama (1999)/Season 02/
temp_original_backup/tv_shows/1080p/Futurama (1999)/Season 03/

# Script 7 processing consolidated input:
5_awaiting_poster/tv_shows/Futurama (1999)/Season 01/
5_awaiting_poster/tv_shows/Futurama (1999)/Season 02/
5_awaiting_poster/tv_shows/Futurama (1999)/Season 03/

# Question: Which resolution folder gets the posters?
```