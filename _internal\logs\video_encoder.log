2025-09-15 05:15:52,643 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-15 05:15:52,643 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-15 05:15:52,643 - INFO - Filesystem-first video encoder initialized
2025-09-15 05:15:52,643 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-15 05:15:52,643 - INFO - Workspace: .
2025-09-15 05:16:13,405 - INFO - Starting filesystem-first video encoding batch processing
2025-09-15 05:16:13,405 - INFO - Discovering movies ready for video encoding...
2025-09-15 05:16:13,500 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-15 05:16:13,535 - INFO - Got actual duration from ffprobe: 5312.3 seconds (88.5 minutes)
2025-09-15 05:16:13,557 - INFO - Got actual duration from ffprobe: 9133.3 seconds (152.2 minutes)
2025-09-15 05:16:13,578 - INFO - Got actual duration from ffprobe: 7926.4 seconds (132.1 minutes)
2025-09-15 05:16:13,595 - INFO - Got actual duration from ffprobe: 7814.5 seconds (130.2 minutes)
2025-09-15 05:16:13,595 - INFO - Found 5 movies ready for encoding
2025-09-15 05:16:35,709 - INFO - Processing movie: 13 Going on 30 (2004)
2025-09-15 05:16:35,710 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-15 05:16:35,710 - INFO - Compressed to 50.0% of original: 14.2 Mbps
2025-09-15 05:17:59,926 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-15 05:17:59,926 - INFO - Starting encoding for 13 Going on 30
2025-09-15 05:17:59,926 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:20:40,082 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-15 06:20:40,082 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-15 06:20:40,082 - INFO - Filesystem-first video encoder initialized
2025-09-15 06:20:40,082 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-15 06:20:40,082 - INFO - Workspace: .
2025-09-15 06:20:41,894 - INFO - Starting filesystem-first video encoding batch processing
2025-09-15 06:20:41,894 - INFO - Discovering movies ready for video encoding...
2025-09-15 06:20:41,989 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-15 06:20:42,027 - INFO - Got actual duration from ffprobe: 120.4 seconds (2.0 minutes)
2025-09-15 06:20:42,053 - INFO - Got actual duration from ffprobe: 196.5 seconds (3.3 minutes)
2025-09-15 06:20:42,071 - INFO - Got actual duration from ffprobe: 202.4 seconds (3.4 minutes)
2025-09-15 06:20:42,089 - INFO - Got actual duration from ffprobe: 176.3 seconds (2.9 minutes)
2025-09-15 06:20:42,089 - INFO - Found 5 movies ready for encoding
2025-09-15 06:20:47,228 - INFO - Processing movie: 13 Going on 30 (2004)
2025-09-15 06:20:47,228 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-15 06:20:47,228 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-15 06:21:22,681 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-15 06:21:22,681 - INFO - Starting encoding for 13 Going on 30
2025-09-15 06:21:22,681 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:22:45,361 - INFO - Encoding completed successfully for 13 Going on 30 in 1.4 minutes
2025-09-15 06:22:45,361 - INFO - Output file verification passed: 34.4 MB
2025-09-15 06:22:45,369 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:22:45,369 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\1080p\13 Going on 30 (2004)
2025-09-15 06:22:45,390 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-15 06:22:45,390 - INFO - Successfully processed 13 Going on 30
2025-09-15 06:22:45,390 - INFO - Processing movie: Don't Breathe (2016)
2025-09-15 06:22:45,390 - INFO - Original bitrate for Don't Breathe: 26837.0 kbps (26.8 Mbps)
2025-09-15 06:22:45,390 - INFO - Compressed to 50.0% of original: 13.4 Mbps
2025-09-15 06:23:20,328 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv
2025-09-15 06:23:20,328 - INFO - Starting encoding for Don't Breathe
2025-09-15 06:23:20,328 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\Don't Breathe (2016).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:24:11,145 - INFO - Encoding completed successfully for Don't Breathe in 0.8 minutes
2025-09-15 06:24:11,145 - INFO - Output file verification passed: 30.9 MB
2025-09-15 06:24:11,166 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:24:11,166 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\1080p\Don't Breathe (2016)
2025-09-15 06:24:11,219 - INFO - Saved encoding metadata for Don't Breathe
2025-09-15 06:24:11,219 - INFO - Successfully processed Don't Breathe
2025-09-15 06:24:11,220 - INFO - Processing movie: The Dark Knight (2008)
2025-09-15 06:24:11,220 - INFO - Original bitrate for The Dark Knight: 22097.1 kbps (22.1 Mbps)
2025-09-15 06:24:11,221 - INFO - Compressed to 50.0% of original: 11.0 Mbps
2025-09-15 06:25:05,124 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv
2025-09-15 06:25:05,124 - INFO - Starting encoding for The Dark Knight
2025-09-15 06:25:05,124 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\The Dark Knight (2008).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:26:39,772 - INFO - Encoding completed successfully for The Dark Knight in 1.6 minutes
2025-09-15 06:26:39,773 - INFO - Output file verification passed: 50.9 MB
2025-09-15 06:26:39,777 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:26:39,777 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\1080p\The Dark Knight (2008)
2025-09-15 06:26:39,780 - INFO - Saved encoding metadata for The Dark Knight
2025-09-15 06:26:39,781 - INFO - Successfully processed The Dark Knight
2025-09-15 06:26:39,781 - INFO - Processing movie: Star Trek Into Darkness (2013)
2025-09-15 06:26:39,781 - INFO - Original bitrate for Star Trek Into Darkness: 59189.5 kbps (59.2 Mbps)
2025-09-15 06:26:39,781 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:28:34,753 - INFO - Output path: workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv
2025-09-15 06:28:34,753 - INFO - Starting encoding for Star Trek Into Darkness
2025-09-15 06:28:34,753 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).processed.mkv" -o "workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main10 --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:31:56,499 - INFO - Encoding completed successfully for Star Trek Into Darkness in 3.4 minutes
2025-09-15 06:31:56,499 - INFO - Output file verification passed: 52.1 MB
2025-09-15 06:31:56,503 - WARNING - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:31:56,503 - WARNING - Stage 3 movie directory not found: workspace\3_mkv_cleaned_subtitles_extracted\4k\Star Trek Into Darkness (2013)
2025-09-15 06:31:56,548 - INFO - Saved encoding metadata for Star Trek Into Darkness
2025-09-15 06:31:56,549 - INFO - Successfully processed Star Trek Into Darkness
2025-09-15 06:31:56,549 - INFO - Processing movie: Top Gun Maverick (2022)
2025-09-15 06:31:56,549 - INFO - Original bitrate for Top Gun Maverick: 75466.7 kbps (75.5 Mbps)
2025-09-15 06:31:56,549 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:31:57,572 - INFO - Batch processing interrupted
2025-09-15 06:31:57,572 - INFO - Batch processing complete: 4 successful, 0 failed, 0 skipped
2025-09-15 06:49:07,487 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-15 06:49:07,488 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-15 06:49:07,488 - INFO - Filesystem-first video encoder initialized
2025-09-15 06:49:07,488 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-15 06:49:07,488 - INFO - Workspace: .
2025-09-15 06:49:09,527 - INFO - Starting filesystem-first video encoding batch processing
2025-09-15 06:49:09,527 - INFO - Discovering movies ready for video encoding...
2025-09-15 06:49:09,607 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-15 06:49:09,643 - INFO - Got actual duration from ffprobe: 120.4 seconds (2.0 minutes)
2025-09-15 06:49:09,669 - INFO - Got actual duration from ffprobe: 196.5 seconds (3.3 minutes)
2025-09-15 06:49:09,687 - INFO - Got actual duration from ffprobe: 202.4 seconds (3.4 minutes)
2025-09-15 06:49:09,706 - INFO - Got actual duration from ffprobe: 176.3 seconds (2.9 minutes)
2025-09-15 06:49:09,706 - INFO - Found 5 movies ready for encoding
2025-09-15 06:49:13,485 - INFO - Processing movie: 13 Going on 30 (2004)
2025-09-15 06:49:13,486 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-15 06:49:13,486 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-15 06:49:54,198 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-15 06:49:54,198 - INFO - Starting encoding for 13 Going on 30
2025-09-15 06:49:54,199 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:51:13,403 - INFO - Encoding completed successfully for 13 Going on 30 in 1.3 minutes
2025-09-15 06:51:13,403 - INFO - Output file verification passed: 34.4 MB
2025-09-15 06:51:13,405 - INFO - 🧹 Cleaning up after video_encoding completion for 13 Going on 30 (2004)
2025-09-15 06:51:13,406 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:51:13,406 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:51:13,406 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:51:13,411 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-15 06:51:13,411 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:51:13,411 - INFO -    To: workspace\4_ready_for_final_mux\1080p\13 Going on 30 (2004)
2025-09-15 06:51:13,441 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-15 06:51:13,444 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-15 06:51:13,444 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-15 06:51:13,447 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-15 06:51:13,447 - INFO - Successfully processed 13 Going on 30
2025-09-15 06:51:13,447 - INFO - Processing movie: Don't Breathe (2016)
2025-09-15 06:51:13,448 - INFO - Original bitrate for Don't Breathe: 26837.0 kbps (26.8 Mbps)
2025-09-15 06:51:13,448 - INFO - Compressed to 50.0% of original: 13.4 Mbps
2025-09-15 06:51:37,464 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv
2025-09-15 06:51:37,464 - INFO - Starting encoding for Don't Breathe
2025-09-15 06:51:37,464 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\Don't Breathe (2016).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)\Dont Breathe (2016).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:52:28,462 - INFO - Encoding completed successfully for Don't Breathe in 0.8 minutes
2025-09-15 06:52:28,462 - INFO - Output file verification passed: 30.9 MB
2025-09-15 06:52:28,464 - INFO - 🧹 Cleaning up after video_encoding completion for Don't Breathe (2016)
2025-09-15 06:52:28,464 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:52:28,464 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:52:28,464 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:52:28,475 - INFO - ✅ Copied largest audio file: Track_1_eng_A_DTS.dts (51.6 MB)
2025-09-15 06:52:28,475 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:52:28,475 - INFO -    To: workspace\4_ready_for_final_mux\1080p\Don't Breathe (2016)
2025-09-15 06:52:28,501 - INFO - 🗑️ Deleted processed MKV file: Don't Breathe (2016).processed.mkv
2025-09-15 06:52:28,507 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\Don't Breathe (2016)\_Processed_Audio
2025-09-15 06:52:28,507 - INFO - ✅ Stage 3 cleanup completed for Don't Breathe
2025-09-15 06:52:28,512 - INFO - Saved encoding metadata for Don't Breathe
2025-09-15 06:52:28,512 - INFO - Successfully processed Don't Breathe
2025-09-15 06:52:28,513 - INFO - Processing movie: The Dark Knight (2008)
2025-09-15 06:52:28,513 - INFO - Original bitrate for The Dark Knight: 22097.1 kbps (22.1 Mbps)
2025-09-15 06:52:28,513 - INFO - Compressed to 50.0% of original: 11.0 Mbps
2025-09-15 06:53:00,250 - INFO - Output path: workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv
2025-09-15 06:53:00,250 - INFO - Starting encoding for The Dark Knight
2025-09-15 06:53:00,250 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\The Dark Knight (2008).processed.mkv" -o "workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)\The Dark Knight (2008).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:54:47,464 - INFO - Encoding completed successfully for The Dark Knight in 1.8 minutes
2025-09-15 06:54:47,464 - INFO - Output file verification passed: 50.9 MB
2025-09-15 06:54:47,466 - INFO - 🧹 Cleaning up after video_encoding completion for The Dark Knight (2008)
2025-09-15 06:54:47,466 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:54:47,466 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:54:47,466 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:54:47,472 - INFO - ✅ Copied largest audio file: Track_1_eng_A_TRUEHD.thd (30.0 MB)
2025-09-15 06:54:47,472 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:54:47,472 - INFO -    To: workspace\4_ready_for_final_mux\1080p\The Dark Knight (2008)
2025-09-15 06:54:47,507 - INFO - 🗑️ Deleted processed MKV file: The Dark Knight (2008).processed.mkv
2025-09-15 06:54:47,515 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\The Dark Knight (2008)\_Processed_Audio
2025-09-15 06:54:47,515 - INFO - ✅ Stage 3 cleanup completed for The Dark Knight
2025-09-15 06:54:47,538 - INFO - Saved encoding metadata for The Dark Knight
2025-09-15 06:54:47,538 - INFO - Successfully processed The Dark Knight
2025-09-15 06:54:47,538 - INFO - Processing movie: Star Trek Into Darkness (2013)
2025-09-15 06:54:47,538 - INFO - Original bitrate for Star Trek Into Darkness: 59189.5 kbps (59.2 Mbps)
2025-09-15 06:54:47,538 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:55:14,581 - INFO - Output path: workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv
2025-09-15 06:55:14,581 - INFO - Starting encoding for Star Trek Into Darkness
2025-09-15 06:55:14,581 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).processed.mkv" -o "workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)\Star Trek Into Darkness (2013).encoded.mkv" -e x265 --encoder-preset fast --encoder-profile main10 --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 06:58:26,342 - INFO - Encoding completed successfully for Star Trek Into Darkness in 3.2 minutes
2025-09-15 06:58:26,342 - INFO - Output file verification passed: 52.1 MB
2025-09-15 06:58:26,343 - INFO - 🧹 Cleaning up after video_encoding completion for Star Trek Into Darkness (2013)
2025-09-15 06:58:26,343 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 06:58:26,344 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 06:58:26,344 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:58:26,365 - INFO - ✅ Copied largest audio file: Track_1_eng_A_TRUEHD.thd (104.6 MB)
2025-09-15 06:58:26,366 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:58:26,366 - INFO -    To: workspace\4_ready_for_final_mux\4k\Star Trek Into Darkness (2013)
2025-09-15 06:58:26,459 - INFO - 🗑️ Deleted processed MKV file: Star Trek Into Darkness (2013).processed.mkv
2025-09-15 06:58:26,470 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Star Trek Into Darkness (2013)\_Processed_Audio
2025-09-15 06:58:26,470 - INFO - ✅ Stage 3 cleanup completed for Star Trek Into Darkness
2025-09-15 06:58:26,474 - INFO - Saved encoding metadata for Star Trek Into Darkness
2025-09-15 06:58:26,474 - INFO - Successfully processed Star Trek Into Darkness
2025-09-15 06:58:26,474 - INFO - Processing movie: Top Gun Maverick (2022)
2025-09-15 06:58:26,474 - INFO - Original bitrate for Top Gun Maverick: 75466.7 kbps (75.5 Mbps)
2025-09-15 06:58:26,474 - INFO - 4K content: using default 20.0 Mbps
2025-09-15 06:59:01,344 - INFO - Output path: workspace\4_ready_for_final_mux\4k\Top Gun Maverick (2022)\Top Gun Maverick (2022).encoded.mkv
2025-09-15 06:59:01,344 - INFO - Starting encoding for Top Gun Maverick
2025-09-15 06:59:01,344 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\Top Gun Maverick (2022).processed.mkv" -o "workspace\4_ready_for_final_mux\4k\Top Gun Maverick (2022)\Top Gun Maverick (2022).encoded.mkv" -e x265 --encoder-preset slow --encoder-profile main --encoder-level 5.1 -f mkv -b 2000 --multi-pass -T --audio 1 -E aac -B 160 --mixdown stereo --verbose 1
2025-09-15 07:06:05,456 - INFO - Encoding completed successfully for Top Gun Maverick in 7.1 minutes
2025-09-15 07:06:05,456 - INFO - Output file verification passed: 45.7 MB
2025-09-15 07:06:05,458 - INFO - 🧹 Cleaning up after video_encoding completion for Top Gun Maverick (2022)
2025-09-15 07:06:05,458 - INFO - ✅ Cleanup completed for video_encoding
2025-09-15 07:06:05,458 - INFO - ✅ Cleanup completed after video encoding
2025-09-15 07:06:05,458 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\_Processed_Audio
2025-09-15 07:06:05,479 - INFO - ✅ Copied largest audio file: Track_1_eng_A_TRUEHD.thd (101.5 MB)
2025-09-15 07:06:05,479 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\_Processed_Audio
2025-09-15 07:06:05,480 - INFO -    To: workspace\4_ready_for_final_mux\4k\Top Gun Maverick (2022)
2025-09-15 07:06:05,583 - INFO - 🗑️ Deleted processed MKV file: Top Gun Maverick (2022).processed.mkv
2025-09-15 07:06:05,590 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\4k\Top Gun Maverick (2022)\_Processed_Audio
2025-09-15 07:06:05,591 - INFO - ✅ Stage 3 cleanup completed for Top Gun Maverick
2025-09-15 07:06:05,595 - INFO - Saved encoding metadata for Top Gun Maverick
2025-09-15 07:06:05,595 - INFO - Successfully processed Top Gun Maverick
2025-09-15 07:06:05,595 - INFO - Batch processing complete: 5 successful, 0 failed, 0 skipped
2025-09-16 22:13:49,304 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 22:13:49,309 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 22:13:49,310 - INFO - Filesystem-first video encoder initialized
2025-09-16 22:13:49,310 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 22:13:49,310 - INFO - Workspace: .
2025-09-16 22:37:31,071 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 22:37:31,071 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 22:37:31,072 - INFO - Filesystem-first video encoder initialized
2025-09-16 22:37:31,072 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 22:37:31,072 - INFO - Workspace: .
2025-09-16 22:37:33,347 - INFO - Starting filesystem-first TV episode encoding batch processing
2025-09-16 22:37:33,348 - INFO - Discovering TV episodes ready for video encoding...
2025-09-16 22:37:33,348 - INFO - Found 0 episodes ready for encoding
2025-09-16 22:37:33,348 - INFO - No episodes ready for encoding
2025-09-16 22:38:11,888 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 22:38:11,888 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 22:38:11,888 - INFO - Filesystem-first video encoder initialized
2025-09-16 22:38:11,888 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 22:38:11,888 - INFO - Workspace: .
2025-09-16 22:38:13,163 - INFO - Starting filesystem-first TV episode encoding batch processing
2025-09-16 22:38:13,163 - INFO - Discovering TV episodes ready for video encoding...
2025-09-16 22:38:13,164 - INFO - Found 0 episodes ready for encoding
2025-09-16 22:38:13,164 - INFO - No episodes ready for encoding
2025-09-16 23:05:33,394 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:05:33,395 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:05:33,396 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:05:33,396 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:05:33,396 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:05:33,396 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:05:33,396 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:05:33,396 - INFO - Workspace: .
2025-09-16 23:05:33,397 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:05:33,423 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:05:33,423 - INFO - Found 1 movies ready for encoding
2025-09-16 23:05:33,423 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-16 23:05:33,424 - INFO - Compressed to 50.0% of original: 14.2 Mbps
2025-09-16 23:05:38,912 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:05:38,912 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:05:38,914 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:05:38,914 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:05:38,914 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:05:38,914 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:05:38,914 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:05:38,914 - INFO - Workspace: .
2025-09-16 23:05:38,914 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:05:38,938 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:05:38,939 - INFO - Found 1 movies ready for encoding
2025-09-16 23:05:38,939 - ERROR - Error in queue-based processing: EOF when reading a line
2025-09-16 23:09:57,369 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:09:57,370 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:09:57,371 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:09:57,371 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:09:57,371 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:09:57,371 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:09:57,371 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:09:57,371 - INFO - Workspace: .
2025-09-16 23:09:57,371 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:09:57,395 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:09:57,395 - INFO - Found 1 movies ready for encoding
2025-09-16 23:09:57,395 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-16 23:09:57,395 - INFO - Compressed to 50.0% of original: 14.2 Mbps
2025-09-16 23:10:19,816 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:10:19,816 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:10:19,817 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:10:19,818 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:10:19,818 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:10:19,818 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:10:19,818 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:10:19,818 - INFO - Workspace: .
2025-09-16 23:10:19,818 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:10:19,841 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:10:19,842 - INFO - Found 1 movies ready for encoding
2025-09-16 23:10:19,842 - ERROR - Error in queue-based processing: EOF when reading a line
2025-09-16 23:13:03,865 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:13:03,865 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:13:03,866 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:13:03,866 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:13:03,867 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:13:03,867 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:13:03,867 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:13:03,867 - INFO - Workspace: .
2025-09-16 23:13:03,867 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:13:03,891 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:13:03,891 - INFO - Found 1 movies ready for encoding
2025-09-16 23:13:31,910 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-16 23:13:31,911 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-16 23:13:31,912 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-16 23:13:31,912 - INFO - Filesystem-first video encoder initialized
2025-09-16 23:13:31,912 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-16 23:13:31,912 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-16 23:13:31,912 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-16 23:13:31,912 - INFO - Workspace: .
2025-09-16 23:13:31,912 - INFO - Discovering movies ready for video encoding...
2025-09-16 23:13:31,936 - INFO - Got actual duration from ffprobe: 5867.9 seconds (97.8 minutes)
2025-09-16 23:13:31,936 - INFO - Found 1 movies ready for encoding
2025-09-16 23:13:59,131 - INFO - Original bitrate for 13 Going on 30: 28499.5 kbps (28.5 Mbps)
2025-09-16 23:13:59,131 - INFO - Compressed to 50.0% of original: 14.2 Mbps
2025-09-17 00:33:07,558 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:33:07,559 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:33:07,568 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:33:07,568 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:33:07,568 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:33:07,568 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:33:07,568 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:33:07,568 - INFO - Workspace: .
2025-09-17 00:33:56,474 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:33:56,475 - INFO - Found 0 episodes ready for encoding
2025-09-17 00:34:17,500 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:34:17,500 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:34:17,502 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:34:17,502 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:34:17,502 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:34:17,503 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:34:17,503 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:34:17,503 - INFO - Workspace: .
2025-09-17 00:34:19,208 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:34:19,209 - INFO - Found 0 episodes ready for encoding
2025-09-17 00:39:35,565 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:39:35,565 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:39:35,566 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:39:35,566 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:39:35,566 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:39:35,566 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:39:35,567 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:39:35,567 - INFO - Workspace: .
2025-09-17 00:39:35,567 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:39:35,567 - INFO - Found 0 episodes ready for encoding
2025-09-17 00:41:06,407 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:41:06,407 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:41:06,409 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:41:06,409 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:41:06,409 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:41:06,409 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:41:06,409 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:41:06,409 - INFO - Workspace: .
2025-09-17 00:41:06,409 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:41:06,410 - ERROR - Error converting episode data: 'FilesystemFirstStateManager' object has no attribute 'generate_episode_identifier'
2025-09-17 00:41:06,410 - INFO - Found 0 episodes ready for encoding
2025-09-17 00:41:41,915 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:41:41,915 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:41:41,916 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:41:41,916 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:41:41,916 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:41:41,917 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:41:41,917 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:41:41,917 - INFO - Workspace: .
2025-09-17 00:41:41,917 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:41:42,172 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 00:41:42,172 - ERROR - Error converting episode data: EpisodeInfo.__init__() got an unexpected keyword argument 'year'
2025-09-17 00:41:42,172 - INFO - Found 0 episodes ready for encoding
2025-09-17 00:42:11,655 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:42:11,655 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:42:11,657 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:42:11,657 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:42:11,657 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:42:11,657 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:42:11,657 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:42:11,657 - INFO - Workspace: .
2025-09-17 00:42:11,657 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:42:11,679 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 00:42:11,679 - INFO - Found 1 episodes ready for encoding
2025-09-17 00:42:45,015 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 00:42:45,016 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 00:42:45,016 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 00:42:45,016 - WARNING - Unknown stage marker: encoding_started
2025-09-17 00:42:45,017 - INFO - Built episode HandBrake command for Futurama (1999) S01E01
2025-09-17 00:42:45,017 - ERROR - Error in episode full encode: 'EpisodeInfo' object has no attribute 'title'
2025-09-17 00:44:36,779 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:44:36,779 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:44:36,781 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:44:36,781 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:44:36,781 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:44:36,782 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:44:36,782 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:44:36,782 - INFO - Workspace: .
2025-09-17 00:44:48,601 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:44:48,622 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 00:44:48,622 - INFO - Found 1 episodes ready for encoding
2025-09-17 00:45:04,997 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 00:45:04,998 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 00:45:04,998 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 00:45:04,998 - WARNING - Unknown stage marker: encoding_started
2025-09-17 00:45:04,998 - INFO - Built episode HandBrake command for Futurama (1999) S01E01
2025-09-17 00:45:04,998 - INFO - Starting encoding for Futurama (1999) S01E01
2025-09-17 00:45:04,998 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.processed.mkv" -o "workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Futurama 1999_S01E01_S01E01processed.encoded.mkv" --encoder x265 --encoder-preset slow --encoder-profile main --encoder-level 4.1 --vb 7000 --two-pass --no-optimize --audio 1 --aencoder aac --ab 128 --mixdown stereo --subtitle scan --subtitle-forced --subtitle-burn --format av_mkv --decomb --loose-anamorphic --verbose=1 --turbo
2025-09-17 00:45:05,462 - INFO - Encoding completed successfully for Futurama (1999) S01E01 in 0.0 minutes
2025-09-17 00:45:05,462 - ERROR - Error in episode full encode: FilesystemFirstVideoEncoder.verify_output_file() missing 1 required positional argument: 'expected_duration'
2025-09-17 00:46:58,041 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:46:58,041 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:46:58,042 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:46:58,042 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:46:58,042 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:46:58,042 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:46:58,042 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:46:58,042 - INFO - Workspace: .
2025-09-17 00:46:58,042 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:46:58,043 - INFO - Found 0 episodes ready for encoding
2025-09-17 00:47:12,197 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:47:12,197 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:47:12,198 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:47:12,198 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:47:12,198 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:47:12,198 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:47:12,198 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:47:12,198 - INFO - Workspace: .
2025-09-17 00:47:12,198 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:47:12,199 - INFO - Found 0 episodes ready for encoding
2025-09-17 00:52:32,947 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:52:32,947 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:52:32,948 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:52:32,948 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:52:32,948 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:52:32,948 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:52:32,949 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:52:32,949 - INFO - Workspace: .
2025-09-17 00:52:40,035 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:52:40,058 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 00:52:40,058 - INFO - Found 1 episodes ready for encoding
2025-09-17 00:53:03,502 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 00:53:03,502 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 00:53:03,502 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 00:53:03,503 - WARNING - Unknown stage marker: encoding_started
2025-09-17 00:53:03,503 - INFO - Built episode HandBrake command for Futurama (1999) S01E01
2025-09-17 00:53:03,504 - INFO - Starting encoding for Futurama (1999) S01E01
2025-09-17 00:53:03,504 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.processed.mkv" -o "workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Futurama 1999_S01E01_S01E01processed.encoded.mkv" --encoder x265 --encoder-preset slow --encoder-profile main --encoder-level 4.1 --vb 7000 --two-pass --no-optimize --audio 1 --aencoder aac --ab 128 --mixdown stereo --subtitle scan --subtitle-forced --subtitle-burn --format av_mkv --decomb --loose-anamorphic --verbose=1 --turbo
2025-09-17 00:53:03,671 - INFO - Encoding completed successfully for Futurama (1999) S01E01 in 0.0 minutes
2025-09-17 00:53:03,672 - ERROR - Output file not created: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Futurama 1999_S01E01_S01E01processed.encoded.mkv
2025-09-17 00:54:51,653 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 00:54:51,654 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 00:54:51,655 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 00:54:51,655 - INFO - Filesystem-first video encoder initialized
2025-09-17 00:54:51,655 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 00:54:51,655 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 00:54:51,655 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 00:54:51,655 - INFO - Workspace: .
2025-09-17 00:54:51,655 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 00:54:51,677 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 00:54:51,677 - INFO - Found 1 episodes ready for encoding
2025-09-17 00:55:04,848 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 00:55:04,848 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 00:55:04,848 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 00:55:04,849 - WARNING - Unknown stage marker: encoding_started
2025-09-17 00:55:04,849 - INFO - Built episode HandBrake command for Futurama (1999) S01E01
2025-09-17 00:55:04,849 - INFO - Starting encoding for Futurama (1999) S01E01
2025-09-17 00:55:04,849 - INFO - HandBrake command: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe -i "workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.processed.mkv" -o "workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Futurama 1999_S01E01_S01E01processed.encoded.mkv" --encoder x265 --encoder-preset slow --encoder-profile main --encoder-level 4.1 --vb 7000 --multi-pass --no-optimize --audio 1 --aencoder aac --ab 128 --mixdown stereo --subtitle scan --subtitle-forced --subtitle-burn --format av_mkv --decomb --loose-anamorphic --verbose=1 --turbo
2025-09-17 00:58:43,854 - ERROR - Encoding failed for Futurama (1999) S01E01: HandBrakeCLI failed with return code 1
Last output:
Encoding: task 1 of 2, 54.91 % (84.72 fps, avg 82.25 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 54.96 % (84.72 fps, avg 82.25 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 54.99 % (84.72 fps, avg 82.25 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 55.08 % (84.72 fps, avg 82.25 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 55.08 % (84.72 fps, avg 82.25 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 55.14 % (69.89 fps, avg 82.12 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 55.15 % (69.89 fps, avg 82.12 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 55.26 % (69.89 fps, avg 82.12 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 55.26 % (69.89 fps, avg 82.12 fps, ETA 00h02m57s)
Encoding: task 1 of 2, 55.26 % (69.89 fps, avg 82.12 fps, ETA 00h02m57s)
2025-09-17 01:00:29,089 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:00:29,089 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:00:29,090 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:00:29,091 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:00:29,091 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:00:29,091 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:00:29,091 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:00:29,091 - INFO - Workspace: .
2025-09-17 01:00:31,346 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:00:31,367 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:00:31,367 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:01:09,042 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:01:09,042 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:01:09,042 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:01:09,042 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:01:09,042 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Futurama 1999_S01E01_S01E01processed.encoded.mkv
2025-09-17 01:01:09,044 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:01:26,245 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:01:26,246 - INFO - Muxing video with new audio: Futurama 1999_S01E01_S01E01processed.encoded.mkv
2025-09-17 01:01:27,370 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:01:27,372 - ERROR - Unexpected error in audio-only processing for episode: FilesystemFirstVideoEncoder.verify_output_file() missing 1 required positional argument: 'expected_duration'
2025-09-17 01:03:17,337 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:03:17,337 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:03:17,338 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:03:17,339 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:03:17,339 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:03:17,339 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:03:17,339 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:03:17,339 - INFO - Workspace: .
2025-09-17 01:03:17,339 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:03:17,339 - INFO - Found 0 episodes ready for encoding
2025-09-17 01:03:36,103 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:03:36,104 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:03:36,105 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:03:36,105 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:03:36,105 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:03:36,105 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:03:36,105 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:03:36,105 - INFO - Workspace: .
2025-09-17 01:03:36,105 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:03:36,127 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:03:36,127 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:03:47,341 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:03:47,341 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:03:47,342 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:03:47,342 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:03:47,342 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Futurama 1999_S01E01_S01E01processed.encoded.mkv
2025-09-17 01:03:47,343 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:04:04,166 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:04:04,166 - INFO - Muxing video with new audio: Futurama 1999_S01E01_S01E01processed.encoded.mkv
2025-09-17 01:04:05,206 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:04:05,207 - INFO - Output file verification passed: 850.1 MB
2025-09-17 01:04:05,208 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 01:19:09,672 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:19:09,672 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:19:09,673 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:19:09,673 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:19:09,673 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:19:09,673 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:19:09,673 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:19:09,673 - INFO - Workspace: .
2025-09-17 01:19:11,791 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:19:11,818 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:19:11,818 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:19:21,605 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:19:21,605 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:19:21,605 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:19:21,605 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:19:21,606 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:19:21,608 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:19:38,467 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:19:38,468 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 01:19:39,512 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:19:39,514 - INFO - Output file verification passed: 850.1 MB
2025-09-17 01:19:39,524 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 01:25:41,823 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:25:41,824 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:25:41,825 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:25:41,825 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:25:41,825 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:25:41,825 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:25:41,825 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:25:41,825 - INFO - Workspace: .
2025-09-17 01:25:49,139 - INFO - Discovering movies ready for video encoding...
2025-09-17 01:25:49,183 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 01:25:49,183 - INFO - Found 1 movies ready for encoding
2025-09-17 01:25:49,183 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:25:49,204 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:25:49,204 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:26:35,075 - INFO - Processing movie (audio-only): 13 Going on 30 (2004)
2025-09-17 01:26:35,075 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-17 01:26:35,075 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-17 01:26:35,076 - INFO - Output path: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-17 01:26:35,076 - INFO - Extracting and transcoding audio: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:26:36,851 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:26:36,851 - INFO - Muxing video with new audio: 13 Going on 30 (2004).encoded.mkv
2025-09-17 01:26:37,627 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:26:37,627 - INFO - Output file verification passed: 437.1 MB
2025-09-17 01:26:37,628 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:26:37,633 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-17 01:26:37,633 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:26:37,633 - INFO -    To: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)
2025-09-17 01:26:37,663 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:26:37,667 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:26:37,667 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-17 01:26:37,671 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-17 01:26:37,671 - INFO - ✅ Audio-only processing completed for 13 Going on 30
2025-09-17 01:26:46,981 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:26:46,982 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:26:46,982 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:26:46,982 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:26:46,982 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:26:46,983 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:27:03,718 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:27:03,718 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 01:27:04,724 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:27:04,726 - INFO - Output file verification passed: 850.1 MB
2025-09-17 01:27:04,727 - ERROR - Unexpected error in audio-only processing for episode: 'EpisodeInfo' object has no attribute 'title'
2025-09-17 01:27:04,727 - WARNING - Unknown stage marker: encoding_failed
2025-09-17 01:31:04,583 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:31:04,583 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:31:04,585 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:31:04,585 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:31:04,585 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:31:04,585 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:31:04,585 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:31:04,585 - INFO - Workspace: .
2025-09-17 01:31:05,903 - INFO - Discovering movies ready for video encoding...
2025-09-17 01:31:05,949 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 01:31:05,949 - INFO - Found 1 movies ready for encoding
2025-09-17 01:31:05,949 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:31:05,970 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:31:05,970 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:31:21,179 - INFO - Processing movie (audio-only): 13 Going on 30 (2004)
2025-09-17 01:31:21,179 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-17 01:31:21,179 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-17 01:31:21,179 - INFO - Output path: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-17 01:31:21,180 - INFO - Extracting and transcoding audio: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:31:22,971 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:31:22,972 - INFO - Muxing video with new audio: 13 Going on 30 (2004).encoded.mkv
2025-09-17 01:31:23,726 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:31:23,727 - INFO - Output file verification passed: 437.1 MB
2025-09-17 01:31:23,728 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:31:23,733 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-17 01:31:23,734 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:31:23,734 - INFO -    To: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)
2025-09-17 01:31:23,763 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:31:23,767 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:31:23,767 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-17 01:31:23,859 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-17 01:31:23,859 - INFO - ✅ Audio-only processing completed for 13 Going on 30
2025-09-17 01:31:28,915 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:31:28,915 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:31:28,915 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:31:28,915 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:31:28,916 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:31:28,917 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:31:45,749 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:31:45,749 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 01:31:46,957 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:31:46,959 - INFO - Output file verification passed: 850.1 MB
2025-09-17 01:31:46,959 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 01:34:57,107 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:34:57,107 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:34:57,109 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:34:57,109 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:34:57,109 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:34:57,109 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:34:57,109 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:34:57,109 - INFO - Workspace: .
2025-09-17 01:34:59,251 - INFO - Discovering movies ready for video encoding...
2025-09-17 01:34:59,294 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 01:34:59,294 - INFO - Found 1 movies ready for encoding
2025-09-17 01:34:59,294 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:34:59,314 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:34:59,314 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:35:16,596 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:35:16,597 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:35:16,598 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:35:16,598 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:35:16,598 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:35:16,598 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:35:16,598 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:35:16,598 - INFO - Workspace: .
2025-09-17 01:35:18,059 - INFO - Discovering movies ready for video encoding...
2025-09-17 01:35:18,104 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 01:35:18,104 - INFO - Found 1 movies ready for encoding
2025-09-17 01:35:18,105 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:35:18,124 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:35:18,124 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:35:28,116 - INFO - Processing movie (audio-only): 13 Going on 30 (2004)
2025-09-17 01:35:28,116 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-17 01:35:28,116 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-17 01:35:28,117 - INFO - Output path: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-17 01:35:28,118 - INFO - Extracting and transcoding audio: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:35:29,900 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:35:29,900 - INFO - Muxing video with new audio: 13 Going on 30 (2004).encoded.mkv
2025-09-17 01:35:30,534 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:35:30,535 - INFO - Output file verification passed: 437.1 MB
2025-09-17 01:35:30,536 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:35:30,541 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-17 01:35:30,541 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:35:30,541 - INFO -    To: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)
2025-09-17 01:35:30,569 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:35:30,572 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:35:30,573 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-17 01:35:30,579 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-17 01:35:30,579 - INFO - ✅ Audio-only processing completed for 13 Going on 30
2025-09-17 01:35:34,864 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:35:34,864 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:35:34,864 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:35:34,864 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:35:34,865 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:35:34,865 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:35:51,751 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:35:51,751 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 01:35:52,841 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:35:52,843 - INFO - Output file verification passed: 850.1 MB
2025-09-17 01:35:52,844 - ERROR - Failed to copy audio file for episode: 'EpisodeInfo' object has no attribute 'year'
2025-09-17 01:35:52,844 - WARNING - ⚠️ Audio file copy failed but processing continues: 'EpisodeInfo' object has no attribute 'year'
2025-09-17 01:35:52,844 - ERROR - Failed to cleanup stage 3 files for episode: 'EpisodeInfo' object has no attribute 'year'
2025-09-17 01:35:52,845 - WARNING - ⚠️ Stage 3 cleanup failed but processing continues: 'EpisodeInfo' object has no attribute 'year'
2025-09-17 01:35:52,845 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 01:39:53,617 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:39:53,617 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:39:53,618 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:39:53,618 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:39:53,618 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:39:53,618 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:39:53,618 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:39:53,618 - INFO - Workspace: .
2025-09-17 01:39:55,102 - INFO - Discovering movies ready for video encoding...
2025-09-17 01:39:55,147 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 01:39:55,147 - INFO - Found 1 movies ready for encoding
2025-09-17 01:39:55,147 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:39:55,167 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:39:55,168 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:40:05,969 - INFO - Processing movie (audio-only): 13 Going on 30 (2004)
2025-09-17 01:40:05,969 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-17 01:40:05,969 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-17 01:40:05,970 - INFO - Output path: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-17 01:40:05,971 - INFO - Extracting and transcoding audio: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:40:07,749 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:40:07,749 - INFO - Muxing video with new audio: 13 Going on 30 (2004).encoded.mkv
2025-09-17 01:40:08,480 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:40:08,481 - INFO - Output file verification passed: 437.1 MB
2025-09-17 01:40:08,482 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:40:08,486 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-17 01:40:08,487 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:40:08,487 - INFO -    To: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)
2025-09-17 01:40:08,516 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:40:08,519 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:40:08,519 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-17 01:40:08,698 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-17 01:40:08,699 - INFO - ✅ Audio-only processing completed for 13 Going on 30
2025-09-17 01:40:12,479 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:40:12,479 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:40:12,479 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:40:12,479 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:40:12,480 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:40:12,480 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:40:29,241 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:40:29,241 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 01:40:30,657 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:40:30,659 - INFO - Output file verification passed: 850.1 MB
2025-09-17 01:40:30,659 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:40:30,660 - WARNING - No audio files found in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:40:30,718 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 01:40:30,721 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:40:30,722 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 01:40:30,722 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 01:47:19,441 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:47:19,442 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:47:19,443 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:47:19,443 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:47:19,443 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:47:19,443 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:47:19,443 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:47:19,443 - INFO - Workspace: .
2025-09-17 01:47:24,019 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:47:24,019 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:47:24,020 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:47:24,020 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:47:24,020 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:47:24,021 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:47:24,021 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:47:24,021 - INFO - Workspace: .
2025-09-17 01:47:25,493 - INFO - Discovering movies ready for video encoding...
2025-09-17 01:47:25,535 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 01:47:25,536 - INFO - Found 1 movies ready for encoding
2025-09-17 01:47:25,536 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:47:25,556 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 01:47:25,556 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:47:34,331 - INFO - Processing movie (audio-only): 13 Going on 30 (2004)
2025-09-17 01:47:34,331 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-17 01:47:34,331 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-17 01:47:34,332 - INFO - Output path: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-17 01:47:34,332 - INFO - Extracting and transcoding audio: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:47:36,131 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:47:36,131 - INFO - Muxing video with new audio: 13 Going on 30 (2004).encoded.mkv
2025-09-17 01:47:36,705 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:47:36,706 - INFO - Output file verification passed: 437.1 MB
2025-09-17 01:47:36,706 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:47:36,711 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-17 01:47:36,712 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:47:36,712 - INFO -    To: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)
2025-09-17 01:47:36,745 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-17 01:47:36,757 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 01:47:36,757 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-17 01:47:36,760 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-17 01:47:36,760 - INFO - ✅ Audio-only processing completed for 13 Going on 30
2025-09-17 01:47:46,178 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:47:46,178 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5270.3 kbps (5.3 Mbps)
2025-09-17 01:47:46,178 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:47:46,178 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:47:46,179 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:47:46,180 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:48:03,117 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:48:03,117 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 01:48:04,126 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:48:04,128 - INFO - Output file verification passed: 850.1 MB
2025-09-17 01:48:04,128 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:48:04,129 - WARNING - No audio files found in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:48:04,187 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 01:48:04,192 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:48:04,192 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 01:48:04,192 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 01:55:55,170 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:55:55,170 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:55:55,171 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:55:55,171 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:55:55,171 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:55:55,171 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:55:55,171 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:55:55,171 - INFO - Workspace: .
2025-09-17 01:55:57,531 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:55:57,554 - INFO - Got actual duration from ffprobe: 1351.6 seconds (22.5 minutes)
2025-09-17 01:55:57,555 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:56:04,840 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:56:04,840 - INFO - Original episode bitrate for Futurama (1999) S01E01: 1076.3 kbps (1.1 Mbps)
2025-09-17 01:56:04,840 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:56:04,840 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:56:04,841 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:56:04,842 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 01:56:23,585 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 01:56:23,585 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 01:56:24,046 - INFO - ✅ Video/audio muxing successful
2025-09-17 01:56:24,047 - INFO - Output file verification passed: 184.3 MB
2025-09-17 01:56:24,048 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:56:24,048 - INFO - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 01:56:24,048 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 01:56:24,049 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 01:59:40,535 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 01:59:40,535 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 01:59:40,537 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 01:59:40,537 - INFO - Filesystem-first video encoder initialized
2025-09-17 01:59:40,537 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 01:59:40,537 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 01:59:40,537 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 01:59:40,537 - INFO - Workspace: .
2025-09-17 01:59:41,876 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 01:59:41,897 - INFO - Got actual duration from ffprobe: 1351.6 seconds (22.5 minutes)
2025-09-17 01:59:41,897 - INFO - Found 1 episodes ready for encoding
2025-09-17 01:59:49,845 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 01:59:49,845 - INFO - Original episode bitrate for Futurama (1999) S01E01: 1076.3 kbps (1.1 Mbps)
2025-09-17 01:59:49,845 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 01:59:49,845 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 01:59:49,846 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 01:59:49,847 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 02:00:08,498 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 02:00:08,498 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 02:00:09,237 - INFO - ✅ Video/audio muxing successful
2025-09-17 02:00:09,241 - INFO - Output file verification passed: 184.3 MB
2025-09-17 02:00:09,245 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 02:00:09,246 - INFO - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 02:00:09,258 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 02:00:09,259 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\720p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 02:00:09,259 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 02:00:09,259 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 02:04:57,005 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 02:04:57,006 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 02:04:57,007 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 02:04:57,007 - INFO - Filesystem-first video encoder initialized
2025-09-17 02:04:57,007 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 02:04:57,007 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 02:04:57,007 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 02:04:57,007 - INFO - Workspace: .
2025-09-17 02:04:58,445 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 02:04:58,465 - INFO - Got actual duration from ffprobe: 1351.6 seconds (22.5 minutes)
2025-09-17 02:04:58,465 - INFO - Found 1 episodes ready for encoding
2025-09-17 02:05:06,760 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 02:05:06,760 - INFO - Original episode bitrate for Futurama (1999) S01E01: 1076.3 kbps (1.1 Mbps)
2025-09-17 02:05:06,760 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 02:05:06,760 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 720p @ 7.0 Mbps
2025-09-17 02:05:06,761 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\720p\Futurama (1999)\Season 01\S01E01.encoded.mkv
2025-09-17 02:05:06,762 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 02:05:25,552 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 02:05:25,552 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 02:05:26,039 - INFO - ✅ Video/audio muxing successful
2025-09-17 02:05:26,040 - INFO - Output file verification passed: 184.3 MB
2025-09-17 02:05:26,041 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\720p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 02:05:26,044 - INFO - ✅ Copied largest audio file: S01E01.aac (10.5 MB)
2025-09-17 02:05:26,045 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\720p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 02:05:26,045 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\720p\Futurama (1999)\Season 01
2025-09-17 02:05:26,057 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 02:05:26,058 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\720p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 02:05:26,058 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 02:05:26,058 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 02:20:48,872 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 02:20:48,872 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 02:20:48,873 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 02:20:48,873 - INFO - Filesystem-first video encoder initialized
2025-09-17 02:20:48,873 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 02:20:48,873 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 02:20:48,873 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 02:20:48,874 - INFO - Workspace: .
2025-09-17 02:20:50,751 - INFO - Discovering movies ready for video encoding...
2025-09-17 02:20:50,797 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 02:20:50,797 - INFO - Found 1 movies ready for encoding
2025-09-17 02:20:58,987 - INFO - Processing movie (audio-only): 13 Going on 30 (2004)
2025-09-17 02:20:58,987 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-17 02:20:58,988 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-17 02:20:58,989 - INFO - Output path: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-17 02:20:58,990 - INFO - Extracting and transcoding audio: 13 Going on 30 (2004).processed.mkv
2025-09-17 02:21:00,799 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 02:21:00,799 - INFO - Muxing video with new audio: 13 Going on 30 (2004).encoded.mkv
2025-09-17 02:21:01,562 - INFO - ✅ Video/audio muxing successful
2025-09-17 02:21:01,563 - INFO - Output file verification passed: 437.1 MB
2025-09-17 02:21:01,564 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 02:21:01,571 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-17 02:21:01,571 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 02:21:01,571 - INFO -    To: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)
2025-09-17 02:21:01,614 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-17 02:21:01,619 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 02:21:01,619 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-17 02:21:01,625 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-17 02:21:01,626 - INFO - ✅ Audio-only processing completed for 13 Going on 30
2025-09-17 15:29:11,626 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 15:29:11,633 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 15:29:11,634 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 15:29:11,634 - INFO - Filesystem-first video encoder initialized
2025-09-17 15:29:11,634 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 15:29:11,634 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 15:29:11,634 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 15:29:11,634 - INFO - Workspace: .
2025-09-17 15:29:26,957 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 15:29:26,957 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 15:29:26,958 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 15:29:26,958 - INFO - Filesystem-first video encoder initialized
2025-09-17 15:29:26,958 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 15:29:26,958 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 15:29:26,958 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 15:29:26,959 - INFO - Workspace: .
2025-09-17 15:29:28,769 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 15:29:29,100 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 15:29:29,123 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:29:29,144 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 15:29:29,167 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 15:29:29,188 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 15:29:29,210 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 15:29:29,232 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 15:29:29,255 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 15:29:29,276 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:29:29,276 - INFO - Found 9 episodes ready for encoding
2025-09-17 15:29:44,881 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 15:29:44,881 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5250.1 kbps (5.3 Mbps)
2025-09-17 15:29:44,881 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:29:44,881 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 15:29:44,882 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01\S01E01.encoded.mkv
2025-09-17 15:29:44,884 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 15:30:02,012 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:30:02,012 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 15:30:03,098 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:30:03,100 - INFO - Output file verification passed: 846.9 MB
2025-09-17 15:30:03,101 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:30:03,107 - INFO - ✅ Copied largest audio file: S01E04.aac (20.6 MB)
2025-09-17 15:30:03,107 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:30:03,107 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01
2025-09-17 15:30:03,157 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 15:30:03,170 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:30:03,170 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 15:30:03,170 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:34:11,677 - INFO - Processing episode (audio-only): Futurama (1999) S01E02
2025-09-17 15:34:11,678 - INFO - Original episode bitrate for Futurama (1999) S01E02: 5587.7 kbps (5.6 Mbps)
2025-09-17 15:34:11,678 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:34:11,678 - INFO - Episode encoding parameters for Futurama (1999) S01E02: 1080p @ 7.0 Mbps
2025-09-17 15:34:11,679 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02\S01E02.encoded.mkv
2025-09-17 15:34:11,680 - INFO - Extracting and transcoding audio: S01E02.processed.mkv
2025-09-17 15:34:29,562 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:34:29,562 - INFO - Muxing video with new audio: S01E02.encoded.mkv
2025-09-17 15:34:30,923 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:34:30,925 - INFO - Output file verification passed: 902.1 MB
2025-09-17 15:34:30,926 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:34:30,926 - INFO - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:34:30,982 - INFO - 🗑️ Deleted processed MKV file: S01E02.processed.mkv
2025-09-17 15:34:30,983 - WARNING - Processed audio folder not found: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:34:30,983 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E02
2025-09-17 15:34:30,983 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:34:33,725 - INFO - Processing episode (audio-only): Futurama (1999) S01E03
2025-09-17 15:34:33,725 - INFO - Original episode bitrate for Futurama (1999) S01E03: 5310.1 kbps (5.3 Mbps)
2025-09-17 15:34:33,726 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:34:33,726 - INFO - Episode encoding parameters for Futurama (1999) S01E03: 1080p @ 7.0 Mbps
2025-09-17 15:34:33,727 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03\S01E03.encoded.mkv
2025-09-17 15:34:33,729 - INFO - Extracting and transcoding audio: S01E03.processed.mkv
2025-09-17 15:34:57,136 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:34:57,136 - INFO - Muxing video with new audio: S01E03.encoded.mkv
2025-09-17 15:34:58,177 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:34:58,179 - INFO - Output file verification passed: 856.9 MB
2025-09-17 15:34:58,180 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:34:58,181 - INFO - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:34:58,239 - INFO - 🗑️ Deleted processed MKV file: S01E03.processed.mkv
2025-09-17 15:34:58,239 - WARNING - Processed audio folder not found: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:34:58,239 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E03
2025-09-17 15:34:58,239 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:35:00,880 - INFO - Processing episode (audio-only): Futurama (1999) S01E04
2025-09-17 15:35:00,880 - INFO - Original episode bitrate for Futurama (1999) S01E04: 5284.7 kbps (5.3 Mbps)
2025-09-17 15:35:00,880 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:35:00,880 - INFO - Episode encoding parameters for Futurama (1999) S01E04: 1080p @ 7.0 Mbps
2025-09-17 15:35:00,881 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04\S01E04.encoded.mkv
2025-09-17 15:35:00,882 - INFO - Extracting and transcoding audio: S01E04.processed.mkv
2025-09-17 15:35:27,142 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:35:27,143 - INFO - Muxing video with new audio: S01E04.encoded.mkv
2025-09-17 15:35:28,128 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:35:28,130 - INFO - Output file verification passed: 853.0 MB
2025-09-17 15:35:28,142 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:35:28,142 - INFO - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:35:28,199 - INFO - 🗑️ Deleted processed MKV file: S01E04.processed.mkv
2025-09-17 15:35:28,199 - WARNING - Processed audio folder not found: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:35:28,200 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E04
2025-09-17 15:35:28,200 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:39:43,783 - INFO - Processing episode (audio-only): Futurama (1999) S01E05
2025-09-17 15:39:43,783 - INFO - Original episode bitrate for Futurama (1999) S01E05: 5942.7 kbps (5.9 Mbps)
2025-09-17 15:39:43,784 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:39:43,784 - INFO - Episode encoding parameters for Futurama (1999) S01E05: 1080p @ 7.0 Mbps
2025-09-17 15:39:43,784 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05\S01E05.encoded.mkv
2025-09-17 15:39:43,785 - INFO - Extracting and transcoding audio: S01E05.processed.mkv
2025-09-17 15:40:05,573 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:40:05,573 - INFO - Muxing video with new audio: S01E05.encoded.mkv
2025-09-17 15:40:06,659 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:40:06,661 - INFO - Output file verification passed: 959.0 MB
2025-09-17 15:40:06,662 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:40:06,662 - INFO - No _Processed_Audio directory found at: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:40:06,720 - INFO - 🗑️ Deleted processed MKV file: S01E05.processed.mkv
2025-09-17 15:40:06,720 - WARNING - Processed audio folder not found: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:40:06,720 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E05
2025-09-17 15:40:06,721 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:41:05,154 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 15:41:05,154 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 15:41:05,156 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 15:41:05,156 - INFO - Filesystem-first video encoder initialized
2025-09-17 15:41:05,156 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 15:41:05,156 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 15:41:05,156 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 15:41:05,156 - INFO - Workspace: .
2025-09-17 15:41:07,484 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 15:41:07,509 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 15:41:07,531 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:41:07,552 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 15:41:07,575 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 15:41:07,596 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 15:41:07,618 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 15:41:07,641 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 15:41:07,664 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 15:41:07,685 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:41:07,685 - INFO - Found 9 episodes ready for encoding
2025-09-17 15:41:32,941 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 15:41:32,941 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5250.1 kbps (5.3 Mbps)
2025-09-17 15:41:32,941 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:41:32,941 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 15:41:32,942 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01\S01E01.encoded.mkv
2025-09-17 15:41:32,943 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 15:41:49,918 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:41:49,918 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 15:41:50,769 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:41:50,771 - INFO - Output file verification passed: 846.9 MB
2025-09-17 15:41:50,771 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:41:50,772 - INFO - 📍 Found matching audio file for S01E01: S01E01.aac
2025-09-17 15:41:50,777 - INFO - ✅ Copied audio file: S01E01.aac (20.6 MB)
2025-09-17 15:41:50,777 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:41:50,777 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01
2025-09-17 15:41:50,827 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 15:41:50,827 - INFO - ⏳ Season 01 encoding in progress - 8 episodes remaining
2025-09-17 15:41:50,828 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:41:50,828 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 15:41:50,828 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:41:58,981 - INFO - Processing episode (audio-only): Futurama (1999) S01E02
2025-09-17 15:41:58,981 - INFO - Original episode bitrate for Futurama (1999) S01E02: 5587.7 kbps (5.6 Mbps)
2025-09-17 15:41:58,981 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:41:58,981 - INFO - Episode encoding parameters for Futurama (1999) S01E02: 1080p @ 7.0 Mbps
2025-09-17 15:41:58,981 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02\S01E02.encoded.mkv
2025-09-17 15:41:58,982 - INFO - Extracting and transcoding audio: S01E02.processed.mkv
2025-09-17 15:42:17,228 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:42:17,228 - INFO - Muxing video with new audio: S01E02.encoded.mkv
2025-09-17 15:42:18,392 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:42:18,394 - INFO - Output file verification passed: 902.1 MB
2025-09-17 15:42:18,395 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:42:18,396 - INFO - 📍 Found matching audio file for S01E02: S01E02.aac
2025-09-17 15:42:18,400 - INFO - ✅ Copied audio file: S01E02.aac (20.6 MB)
2025-09-17 15:42:18,400 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:42:18,400 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02
2025-09-17 15:42:18,459 - INFO - 🗑️ Deleted processed MKV file: S01E02.processed.mkv
2025-09-17 15:42:18,459 - INFO - ⏳ Season 01 encoding in progress - 7 episodes remaining
2025-09-17 15:42:18,459 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:42:18,460 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E02
2025-09-17 15:42:18,460 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:42:27,608 - INFO - Processing episode (audio-only): Futurama (1999) S01E03
2025-09-17 15:42:27,608 - INFO - Original episode bitrate for Futurama (1999) S01E03: 5310.1 kbps (5.3 Mbps)
2025-09-17 15:42:27,609 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:42:27,609 - INFO - Episode encoding parameters for Futurama (1999) S01E03: 1080p @ 7.0 Mbps
2025-09-17 15:42:27,611 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03\S01E03.encoded.mkv
2025-09-17 15:42:27,612 - INFO - Extracting and transcoding audio: S01E03.processed.mkv
2025-09-17 15:42:51,016 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:42:51,016 - INFO - Muxing video with new audio: S01E03.encoded.mkv
2025-09-17 15:42:51,939 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:42:51,941 - INFO - Output file verification passed: 856.9 MB
2025-09-17 15:42:51,942 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:42:51,942 - INFO - 📍 Found matching audio file for S01E03: S01E03.aac
2025-09-17 15:42:51,947 - INFO - ✅ Copied audio file: S01E03.aac (20.6 MB)
2025-09-17 15:42:51,947 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:42:51,947 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03
2025-09-17 15:42:52,003 - INFO - 🗑️ Deleted processed MKV file: S01E03.processed.mkv
2025-09-17 15:42:52,003 - INFO - ⏳ Season 01 encoding in progress - 6 episodes remaining
2025-09-17 15:42:52,003 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:42:52,003 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E03
2025-09-17 15:42:52,004 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:42:52,910 - INFO - Processing episode (audio-only): Futurama (1999) S01E04
2025-09-17 15:42:52,910 - INFO - Original episode bitrate for Futurama (1999) S01E04: 5284.7 kbps (5.3 Mbps)
2025-09-17 15:42:52,910 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:42:52,910 - INFO - Episode encoding parameters for Futurama (1999) S01E04: 1080p @ 7.0 Mbps
2025-09-17 15:42:52,910 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04\S01E04.encoded.mkv
2025-09-17 15:42:52,911 - INFO - Extracting and transcoding audio: S01E04.processed.mkv
2025-09-17 15:43:16,702 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:43:16,702 - INFO - Muxing video with new audio: S01E04.encoded.mkv
2025-09-17 15:43:17,717 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:43:17,719 - INFO - Output file verification passed: 853.0 MB
2025-09-17 15:43:17,806 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:43:17,806 - INFO - 📍 Found matching audio file for S01E04: S01E04.aac
2025-09-17 15:43:17,811 - INFO - ✅ Copied audio file: S01E04.aac (20.6 MB)
2025-09-17 15:43:17,811 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:43:17,811 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04
2025-09-17 15:43:17,868 - INFO - 🗑️ Deleted processed MKV file: S01E04.processed.mkv
2025-09-17 15:43:17,868 - INFO - ⏳ Season 01 encoding in progress - 5 episodes remaining
2025-09-17 15:43:17,869 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:43:17,869 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E04
2025-09-17 15:43:17,869 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:43:20,499 - INFO - Processing episode (audio-only): Futurama (1999) S01E05
2025-09-17 15:43:20,500 - INFO - Original episode bitrate for Futurama (1999) S01E05: 5942.7 kbps (5.9 Mbps)
2025-09-17 15:43:20,500 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:43:20,500 - INFO - Episode encoding parameters for Futurama (1999) S01E05: 1080p @ 7.0 Mbps
2025-09-17 15:43:20,500 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05\S01E05.encoded.mkv
2025-09-17 15:43:20,501 - INFO - Extracting and transcoding audio: S01E05.processed.mkv
2025-09-17 15:43:42,406 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:43:42,406 - INFO - Muxing video with new audio: S01E05.encoded.mkv
2025-09-17 15:43:43,525 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:43:43,527 - INFO - Output file verification passed: 959.0 MB
2025-09-17 15:43:43,528 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:43:43,529 - INFO - 📍 Found matching audio file for S01E05: S01E05.aac
2025-09-17 15:43:43,535 - INFO - ✅ Copied audio file: S01E05.aac (20.6 MB)
2025-09-17 15:43:43,535 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:43:43,535 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05
2025-09-17 15:43:43,596 - INFO - 🗑️ Deleted processed MKV file: S01E05.processed.mkv
2025-09-17 15:43:43,596 - INFO - ⏳ Season 01 encoding in progress - 4 episodes remaining
2025-09-17 15:43:43,596 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:43:43,596 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E05
2025-09-17 15:43:43,597 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:43:44,724 - INFO - Processing episode (audio-only): Futurama (1999) S01E06
2025-09-17 15:43:44,724 - INFO - Original episode bitrate for Futurama (1999) S01E06: 6297.5 kbps (6.3 Mbps)
2025-09-17 15:43:44,724 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:43:44,724 - INFO - Episode encoding parameters for Futurama (1999) S01E06: 1080p @ 7.0 Mbps
2025-09-17 15:43:44,724 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06\S01E06.encoded.mkv
2025-09-17 15:43:44,725 - INFO - Extracting and transcoding audio: S01E06.processed.mkv
2025-09-17 15:44:09,511 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:44:09,511 - INFO - Muxing video with new audio: S01E06.encoded.mkv
2025-09-17 15:44:10,785 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:44:10,786 - INFO - Output file verification passed: 1015.3 MB
2025-09-17 15:44:10,849 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:44:10,849 - INFO - 📍 Found matching audio file for S01E06: S01E06.aac
2025-09-17 15:44:10,855 - INFO - ✅ Copied audio file: S01E06.aac (20.6 MB)
2025-09-17 15:44:10,855 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:44:10,855 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06
2025-09-17 15:44:10,935 - INFO - 🗑️ Deleted processed MKV file: S01E06.processed.mkv
2025-09-17 15:44:10,936 - INFO - ⏳ Season 01 encoding in progress - 3 episodes remaining
2025-09-17 15:44:10,936 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:44:10,936 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E06
2025-09-17 15:44:10,936 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:44:25,902 - INFO - Processing episode (audio-only): Futurama (1999) S01E07
2025-09-17 15:44:25,902 - INFO - Original episode bitrate for Futurama (1999) S01E07: 5510.3 kbps (5.5 Mbps)
2025-09-17 15:44:25,902 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:44:25,903 - INFO - Episode encoding parameters for Futurama (1999) S01E07: 1080p @ 7.0 Mbps
2025-09-17 15:44:25,903 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07\S01E07.encoded.mkv
2025-09-17 15:44:25,904 - INFO - Extracting and transcoding audio: S01E07.processed.mkv
2025-09-17 15:44:49,190 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:44:49,190 - INFO - Muxing video with new audio: S01E07.encoded.mkv
2025-09-17 15:44:50,124 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:44:50,125 - INFO - Output file verification passed: 888.9 MB
2025-09-17 15:44:50,126 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:44:50,127 - INFO - 📍 Found matching audio file for S01E07: S01E07.aac
2025-09-17 15:44:50,132 - INFO - ✅ Copied audio file: S01E07.aac (20.6 MB)
2025-09-17 15:44:50,132 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:44:50,132 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07
2025-09-17 15:44:50,190 - INFO - 🗑️ Deleted processed MKV file: S01E07.processed.mkv
2025-09-17 15:44:50,191 - INFO - ⏳ Season 01 encoding in progress - 2 episodes remaining
2025-09-17 15:44:50,191 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:44:50,191 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E07
2025-09-17 15:44:50,191 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:44:52,558 - INFO - Processing episode (audio-only): Futurama (1999) S01E08
2025-09-17 15:44:52,559 - INFO - Original episode bitrate for Futurama (1999) S01E08: 5988.9 kbps (6.0 Mbps)
2025-09-17 15:44:52,559 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:44:52,559 - INFO - Episode encoding parameters for Futurama (1999) S01E08: 1080p @ 7.0 Mbps
2025-09-17 15:44:52,560 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08\S01E08.encoded.mkv
2025-09-17 15:44:52,561 - INFO - Extracting and transcoding audio: S01E08.processed.mkv
2025-09-17 15:45:17,464 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:45:17,465 - INFO - Muxing video with new audio: S01E08.encoded.mkv
2025-09-17 15:45:18,610 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:45:18,611 - INFO - Output file verification passed: 966.4 MB
2025-09-17 15:45:18,612 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:45:18,613 - INFO - 📍 Found matching audio file for S01E08: S01E08.aac
2025-09-17 15:45:18,617 - INFO - ✅ Copied audio file: S01E08.aac (20.6 MB)
2025-09-17 15:45:18,617 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:45:18,617 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08
2025-09-17 15:45:18,682 - INFO - 🗑️ Deleted processed MKV file: S01E08.processed.mkv
2025-09-17 15:45:18,682 - INFO - ⏳ Season 01 encoding in progress - 1 episodes remaining
2025-09-17 15:45:18,682 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:45:18,682 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E08
2025-09-17 15:45:18,683 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:45:20,788 - INFO - Processing episode (audio-only): Futurama (1999) S01E09
2025-09-17 15:45:20,788 - INFO - Original episode bitrate for Futurama (1999) S01E09: 6502.8 kbps (6.5 Mbps)
2025-09-17 15:45:20,788 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:45:20,788 - INFO - Episode encoding parameters for Futurama (1999) S01E09: 1080p @ 7.0 Mbps
2025-09-17 15:45:20,789 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09\S01E09.encoded.mkv
2025-09-17 15:45:20,790 - INFO - Extracting and transcoding audio: S01E09.processed.mkv
2025-09-17 15:45:40,305 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:45:40,305 - INFO - Muxing video with new audio: S01E09.encoded.mkv
2025-09-17 15:45:41,458 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:45:41,459 - INFO - Output file verification passed: 1049.2 MB
2025-09-17 15:45:41,460 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:45:41,461 - INFO - 📍 Found matching audio file for S01E09: S01E09.aac
2025-09-17 15:45:41,465 - INFO - ✅ Copied audio file: S01E09.aac (20.6 MB)
2025-09-17 15:45:41,466 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:45:41,466 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09
2025-09-17 15:45:41,536 - INFO - 🗑️ Deleted processed MKV file: S01E09.processed.mkv
2025-09-17 15:45:41,536 - INFO - 🎯 Season 01 encoding complete - all episodes processed
2025-09-17 15:45:41,550 - INFO - 🗑️ Deleted season _Processed_Audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:45:41,550 - INFO - 🎯 Season 01 fully processed - all shared resources cleaned
2025-09-17 15:45:41,550 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E09
2025-09-17 15:45:41,550 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:53:07,746 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 15:53:07,746 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 15:53:07,748 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 15:53:07,748 - INFO - Filesystem-first video encoder initialized
2025-09-17 15:53:07,748 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 15:53:07,748 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 15:53:07,748 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 15:53:07,748 - INFO - Workspace: .
2025-09-17 15:53:08,914 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 15:53:08,938 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 15:53:08,960 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:53:08,982 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 15:53:09,007 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 15:53:09,027 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 15:53:09,050 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 15:53:09,072 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 15:53:09,094 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 15:53:09,115 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:53:09,115 - INFO - Found 9 episodes ready for encoding
2025-09-17 15:54:36,583 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 15:54:36,583 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 15:54:36,584 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 15:54:36,584 - INFO - Filesystem-first video encoder initialized
2025-09-17 15:54:36,584 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 15:54:36,585 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 15:54:36,585 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 15:54:36,585 - INFO - Workspace: .
2025-09-17 15:54:37,836 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 15:54:37,859 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 15:54:37,881 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:54:37,901 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 15:54:37,924 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 15:54:37,945 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 15:54:37,966 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 15:54:37,989 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 15:54:38,013 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 15:54:38,034 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 15:54:38,035 - INFO - Found 9 episodes ready for encoding
2025-09-17 15:55:03,197 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 15:55:03,197 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5250.1 kbps (5.3 Mbps)
2025-09-17 15:55:03,197 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:55:03,197 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 15:55:03,198 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01\S01E01.encoded.mkv
2025-09-17 15:55:03,200 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 15:55:20,205 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:55:20,206 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 15:55:21,128 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:55:21,130 - INFO - Output file verification passed: 846.9 MB
2025-09-17 15:55:21,131 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:55:21,131 - INFO - 📍 Found matching audio file for S01E01: S01E01.aac
2025-09-17 15:55:21,138 - INFO - ✅ Copied audio file: S01E01.aac (20.6 MB)
2025-09-17 15:55:21,138 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:55:21,138 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01
2025-09-17 15:55:21,187 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 15:55:21,188 - INFO - ⏳ Season 01 encoding in progress - 8 episodes remaining
2025-09-17 15:55:21,188 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:55:21,188 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 15:55:21,189 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:55:21,189 - INFO - Processing episode (audio-only): Futurama (1999) S01E02
2025-09-17 15:55:21,189 - INFO - Original episode bitrate for Futurama (1999) S01E02: 5587.7 kbps (5.6 Mbps)
2025-09-17 15:55:21,189 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:55:21,189 - INFO - Episode encoding parameters for Futurama (1999) S01E02: 1080p @ 7.0 Mbps
2025-09-17 15:55:21,189 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02\S01E02.encoded.mkv
2025-09-17 15:55:21,190 - INFO - Extracting and transcoding audio: S01E02.processed.mkv
2025-09-17 15:55:39,239 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:55:39,239 - INFO - Muxing video with new audio: S01E02.encoded.mkv
2025-09-17 15:55:40,214 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:55:40,216 - INFO - Output file verification passed: 902.1 MB
2025-09-17 15:55:40,218 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:55:40,219 - INFO - 📍 Found matching audio file for S01E02: S01E02.aac
2025-09-17 15:55:40,226 - INFO - ✅ Copied audio file: S01E02.aac (20.6 MB)
2025-09-17 15:55:40,226 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:55:40,226 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02
2025-09-17 15:55:40,300 - INFO - 🗑️ Deleted processed MKV file: S01E02.processed.mkv
2025-09-17 15:55:40,300 - INFO - ⏳ Season 01 encoding in progress - 7 episodes remaining
2025-09-17 15:55:40,300 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:55:40,300 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E02
2025-09-17 15:55:40,301 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:55:40,301 - INFO - Processing episode (audio-only): Futurama (1999) S01E03
2025-09-17 15:55:40,301 - INFO - Original episode bitrate for Futurama (1999) S01E03: 5310.1 kbps (5.3 Mbps)
2025-09-17 15:55:40,301 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:55:40,301 - INFO - Episode encoding parameters for Futurama (1999) S01E03: 1080p @ 7.0 Mbps
2025-09-17 15:55:40,302 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03\S01E03.encoded.mkv
2025-09-17 15:55:40,303 - INFO - Extracting and transcoding audio: S01E03.processed.mkv
2025-09-17 15:56:03,865 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:56:03,865 - INFO - Muxing video with new audio: S01E03.encoded.mkv
2025-09-17 15:56:04,978 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:56:04,980 - INFO - Output file verification passed: 856.9 MB
2025-09-17 15:56:04,981 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:56:04,981 - INFO - 📍 Found matching audio file for S01E03: S01E03.aac
2025-09-17 15:56:04,986 - INFO - ✅ Copied audio file: S01E03.aac (20.6 MB)
2025-09-17 15:56:04,986 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:56:04,986 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03
2025-09-17 15:56:05,045 - INFO - 🗑️ Deleted processed MKV file: S01E03.processed.mkv
2025-09-17 15:56:05,045 - INFO - ⏳ Season 01 encoding in progress - 6 episodes remaining
2025-09-17 15:56:05,045 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:56:05,045 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E03
2025-09-17 15:56:05,046 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:56:05,046 - INFO - Processing episode (audio-only): Futurama (1999) S01E04
2025-09-17 15:56:05,046 - INFO - Original episode bitrate for Futurama (1999) S01E04: 5284.7 kbps (5.3 Mbps)
2025-09-17 15:56:05,046 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:56:05,046 - INFO - Episode encoding parameters for Futurama (1999) S01E04: 1080p @ 7.0 Mbps
2025-09-17 15:56:05,046 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04\S01E04.encoded.mkv
2025-09-17 15:56:05,047 - INFO - Extracting and transcoding audio: S01E04.processed.mkv
2025-09-17 15:56:28,727 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:56:28,728 - INFO - Muxing video with new audio: S01E04.encoded.mkv
2025-09-17 15:56:29,743 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:56:29,746 - INFO - Output file verification passed: 853.0 MB
2025-09-17 15:56:29,812 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:56:29,812 - INFO - 📍 Found matching audio file for S01E04: S01E04.aac
2025-09-17 15:56:29,818 - INFO - ✅ Copied audio file: S01E04.aac (20.6 MB)
2025-09-17 15:56:29,819 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:56:29,819 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04
2025-09-17 15:56:29,870 - INFO - 🗑️ Deleted processed MKV file: S01E04.processed.mkv
2025-09-17 15:56:29,870 - INFO - ⏳ Season 01 encoding in progress - 5 episodes remaining
2025-09-17 15:56:29,870 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:56:29,870 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E04
2025-09-17 15:56:29,870 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:56:29,871 - INFO - Processing episode (audio-only): Futurama (1999) S01E05
2025-09-17 15:56:29,871 - INFO - Original episode bitrate for Futurama (1999) S01E05: 5942.7 kbps (5.9 Mbps)
2025-09-17 15:56:29,871 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:56:29,871 - INFO - Episode encoding parameters for Futurama (1999) S01E05: 1080p @ 7.0 Mbps
2025-09-17 15:56:29,871 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05\S01E05.encoded.mkv
2025-09-17 15:56:29,872 - INFO - Extracting and transcoding audio: S01E05.processed.mkv
2025-09-17 15:56:52,025 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:56:52,025 - INFO - Muxing video with new audio: S01E05.encoded.mkv
2025-09-17 15:56:53,121 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:56:53,123 - INFO - Output file verification passed: 959.0 MB
2025-09-17 15:56:53,124 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:56:53,124 - INFO - 📍 Found matching audio file for S01E05: S01E05.aac
2025-09-17 15:56:53,128 - INFO - ✅ Copied audio file: S01E05.aac (20.6 MB)
2025-09-17 15:56:53,129 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:56:53,129 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05
2025-09-17 15:56:53,189 - INFO - 🗑️ Deleted processed MKV file: S01E05.processed.mkv
2025-09-17 15:56:53,189 - INFO - ⏳ Season 01 encoding in progress - 4 episodes remaining
2025-09-17 15:56:53,189 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:56:53,189 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E05
2025-09-17 15:56:53,190 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:56:53,190 - INFO - Processing episode (audio-only): Futurama (1999) S01E06
2025-09-17 15:56:53,191 - INFO - Original episode bitrate for Futurama (1999) S01E06: 6297.5 kbps (6.3 Mbps)
2025-09-17 15:56:53,191 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:56:53,191 - INFO - Episode encoding parameters for Futurama (1999) S01E06: 1080p @ 7.0 Mbps
2025-09-17 15:56:53,191 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06\S01E06.encoded.mkv
2025-09-17 15:56:53,192 - INFO - Extracting and transcoding audio: S01E06.processed.mkv
2025-09-17 15:57:18,122 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:57:18,122 - INFO - Muxing video with new audio: S01E06.encoded.mkv
2025-09-17 15:57:19,427 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:57:19,429 - INFO - Output file verification passed: 1015.3 MB
2025-09-17 15:57:19,487 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:57:19,487 - INFO - 📍 Found matching audio file for S01E06: S01E06.aac
2025-09-17 15:57:19,492 - INFO - ✅ Copied audio file: S01E06.aac (20.6 MB)
2025-09-17 15:57:19,492 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:57:19,492 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06
2025-09-17 15:57:19,553 - INFO - 🗑️ Deleted processed MKV file: S01E06.processed.mkv
2025-09-17 15:57:19,554 - INFO - ⏳ Season 01 encoding in progress - 3 episodes remaining
2025-09-17 15:57:19,554 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:57:19,554 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E06
2025-09-17 15:57:19,554 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:57:19,554 - INFO - Processing episode (audio-only): Futurama (1999) S01E07
2025-09-17 15:57:19,554 - INFO - Original episode bitrate for Futurama (1999) S01E07: 5510.3 kbps (5.5 Mbps)
2025-09-17 15:57:19,555 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:57:19,555 - INFO - Episode encoding parameters for Futurama (1999) S01E07: 1080p @ 7.0 Mbps
2025-09-17 15:57:19,555 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07\S01E07.encoded.mkv
2025-09-17 15:57:19,555 - INFO - Extracting and transcoding audio: S01E07.processed.mkv
2025-09-17 15:57:43,022 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:57:43,023 - INFO - Muxing video with new audio: S01E07.encoded.mkv
2025-09-17 15:57:44,155 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:57:44,157 - INFO - Output file verification passed: 888.9 MB
2025-09-17 15:57:44,158 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:57:44,158 - INFO - 📍 Found matching audio file for S01E07: S01E07.aac
2025-09-17 15:57:44,163 - INFO - ✅ Copied audio file: S01E07.aac (20.6 MB)
2025-09-17 15:57:44,163 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:57:44,163 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07
2025-09-17 15:57:44,222 - INFO - 🗑️ Deleted processed MKV file: S01E07.processed.mkv
2025-09-17 15:57:44,223 - INFO - ⏳ Season 01 encoding in progress - 2 episodes remaining
2025-09-17 15:57:44,223 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:57:44,223 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E07
2025-09-17 15:57:44,223 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:57:44,223 - INFO - Processing episode (audio-only): Futurama (1999) S01E08
2025-09-17 15:57:44,223 - INFO - Original episode bitrate for Futurama (1999) S01E08: 5988.9 kbps (6.0 Mbps)
2025-09-17 15:57:44,224 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:57:44,224 - INFO - Episode encoding parameters for Futurama (1999) S01E08: 1080p @ 7.0 Mbps
2025-09-17 15:57:44,224 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08\S01E08.encoded.mkv
2025-09-17 15:57:44,225 - INFO - Extracting and transcoding audio: S01E08.processed.mkv
2025-09-17 15:58:09,227 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:58:09,227 - INFO - Muxing video with new audio: S01E08.encoded.mkv
2025-09-17 15:58:10,277 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:58:10,279 - INFO - Output file verification passed: 966.4 MB
2025-09-17 15:58:10,280 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:58:10,280 - INFO - 📍 Found matching audio file for S01E08: S01E08.aac
2025-09-17 15:58:10,285 - INFO - ✅ Copied audio file: S01E08.aac (20.6 MB)
2025-09-17 15:58:10,285 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:58:10,285 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08
2025-09-17 15:58:10,345 - INFO - 🗑️ Deleted processed MKV file: S01E08.processed.mkv
2025-09-17 15:58:10,346 - INFO - ⏳ Season 01 encoding in progress - 1 episodes remaining
2025-09-17 15:58:10,346 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 15:58:10,346 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E08
2025-09-17 15:58:10,346 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 15:58:10,347 - INFO - Processing episode (audio-only): Futurama (1999) S01E09
2025-09-17 15:58:10,347 - INFO - Original episode bitrate for Futurama (1999) S01E09: 6502.8 kbps (6.5 Mbps)
2025-09-17 15:58:10,347 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 15:58:10,347 - INFO - Episode encoding parameters for Futurama (1999) S01E09: 1080p @ 7.0 Mbps
2025-09-17 15:58:10,366 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09\S01E09.encoded.mkv
2025-09-17 15:58:10,367 - INFO - Extracting and transcoding audio: S01E09.processed.mkv
2025-09-17 15:58:29,628 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 15:58:29,629 - INFO - Muxing video with new audio: S01E09.encoded.mkv
2025-09-17 15:58:30,678 - INFO - ✅ Video/audio muxing successful
2025-09-17 15:58:30,680 - INFO - Output file verification passed: 1049.2 MB
2025-09-17 15:58:30,681 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:58:30,681 - INFO - 📍 Found matching audio file for S01E09: S01E09.aac
2025-09-17 15:58:30,686 - INFO - ✅ Copied audio file: S01E09.aac (20.6 MB)
2025-09-17 15:58:30,686 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:58:30,686 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09
2025-09-17 15:58:30,750 - INFO - 🗑️ Deleted processed MKV file: S01E09.processed.mkv
2025-09-17 15:58:30,750 - INFO - 🎯 Season 01 encoding complete - all episodes processed
2025-09-17 15:58:30,764 - INFO - 🗑️ Deleted season _Processed_Audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 15:58:30,764 - INFO - 🎯 Season 01 fully processed - all shared resources cleaned
2025-09-17 15:58:30,764 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E09
2025-09-17 15:58:30,765 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:06:48,443 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 16:06:48,444 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 16:06:48,445 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 16:06:48,445 - INFO - Filesystem-first video encoder initialized
2025-09-17 16:06:48,445 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 16:06:48,445 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 16:06:48,445 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 16:06:48,445 - INFO - Workspace: .
2025-09-17 16:06:49,875 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 16:06:49,903 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 16:06:49,926 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 16:06:49,947 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 16:06:49,969 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 16:06:49,990 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 16:06:50,012 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 16:06:50,035 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 16:06:50,058 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 16:06:50,079 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 16:06:50,080 - INFO - Found 9 episodes ready for encoding
2025-09-17 16:07:03,367 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 16:07:03,368 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5250.1 kbps (5.3 Mbps)
2025-09-17 16:07:03,368 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:07:03,368 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 16:07:03,370 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01\S01E01.encoded.mkv
2025-09-17 16:07:03,372 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 16:07:23,834 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:07:23,834 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 16:07:24,731 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:07:24,733 - INFO - Output file verification passed: 846.9 MB
2025-09-17 16:07:24,734 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:07:24,734 - INFO - 📍 Found matching audio file for S01E01: S01E01.aac
2025-09-17 16:07:24,747 - INFO - ✅ Copied audio file: S01E01.aac (20.6 MB)
2025-09-17 16:07:24,747 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:07:24,747 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01
2025-09-17 16:07:24,803 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 16:07:24,805 - INFO - ⏳ Season 01 encoding in progress - 8 episodes remaining
2025-09-17 16:07:24,805 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:07:24,805 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 16:07:24,805 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:07:24,806 - INFO - Processing episode (audio-only): Futurama (1999) S01E02
2025-09-17 16:07:24,806 - INFO - Original episode bitrate for Futurama (1999) S01E02: 5587.7 kbps (5.6 Mbps)
2025-09-17 16:07:24,806 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:07:24,806 - INFO - Episode encoding parameters for Futurama (1999) S01E02: 1080p @ 7.0 Mbps
2025-09-17 16:07:24,806 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02\S01E02.encoded.mkv
2025-09-17 16:07:24,807 - INFO - Extracting and transcoding audio: S01E02.processed.mkv
2025-09-17 16:07:47,105 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:07:47,106 - INFO - Muxing video with new audio: S01E02.encoded.mkv
2025-09-17 16:07:48,098 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:07:48,101 - INFO - Output file verification passed: 902.1 MB
2025-09-17 16:07:48,102 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:07:48,102 - INFO - 📍 Found matching audio file for S01E02: S01E02.aac
2025-09-17 16:07:48,107 - INFO - ✅ Copied audio file: S01E02.aac (20.6 MB)
2025-09-17 16:07:48,107 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:07:48,107 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02
2025-09-17 16:07:48,180 - INFO - 🗑️ Deleted processed MKV file: S01E02.processed.mkv
2025-09-17 16:07:48,180 - INFO - ⏳ Season 01 encoding in progress - 7 episodes remaining
2025-09-17 16:07:48,180 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:07:48,180 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E02
2025-09-17 16:07:48,180 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:07:48,181 - INFO - Processing episode (audio-only): Futurama (1999) S01E03
2025-09-17 16:07:48,181 - INFO - Original episode bitrate for Futurama (1999) S01E03: 5310.1 kbps (5.3 Mbps)
2025-09-17 16:07:48,181 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:07:48,182 - INFO - Episode encoding parameters for Futurama (1999) S01E03: 1080p @ 7.0 Mbps
2025-09-17 16:07:48,184 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03\S01E03.encoded.mkv
2025-09-17 16:07:48,185 - INFO - Extracting and transcoding audio: S01E03.processed.mkv
2025-09-17 16:08:16,445 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:08:16,445 - INFO - Muxing video with new audio: S01E03.encoded.mkv
2025-09-17 16:08:17,787 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:08:17,789 - INFO - Output file verification passed: 856.9 MB
2025-09-17 16:08:17,790 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:08:17,790 - INFO - 📍 Found matching audio file for S01E03: S01E03.aac
2025-09-17 16:08:17,795 - INFO - ✅ Copied audio file: S01E03.aac (20.6 MB)
2025-09-17 16:08:17,795 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:08:17,795 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03
2025-09-17 16:08:17,856 - INFO - 🗑️ Deleted processed MKV file: S01E03.processed.mkv
2025-09-17 16:08:17,856 - INFO - ⏳ Season 01 encoding in progress - 6 episodes remaining
2025-09-17 16:08:17,856 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:08:17,856 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E03
2025-09-17 16:08:17,857 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:08:17,857 - INFO - Processing episode (audio-only): Futurama (1999) S01E04
2025-09-17 16:08:17,857 - INFO - Original episode bitrate for Futurama (1999) S01E04: 5284.7 kbps (5.3 Mbps)
2025-09-17 16:08:17,857 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:08:17,857 - INFO - Episode encoding parameters for Futurama (1999) S01E04: 1080p @ 7.0 Mbps
2025-09-17 16:08:17,858 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04\S01E04.encoded.mkv
2025-09-17 16:08:17,858 - INFO - Extracting and transcoding audio: S01E04.processed.mkv
2025-09-17 16:08:47,170 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:08:47,170 - INFO - Muxing video with new audio: S01E04.encoded.mkv
2025-09-17 16:08:48,388 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:08:48,390 - INFO - Output file verification passed: 853.0 MB
2025-09-17 16:08:48,497 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:08:48,499 - INFO - 📍 Found matching audio file for S01E04: S01E04.aac
2025-09-17 16:08:48,509 - INFO - ✅ Copied audio file: S01E04.aac (20.6 MB)
2025-09-17 16:08:48,509 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:08:48,509 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04
2025-09-17 16:08:48,584 - INFO - 🗑️ Deleted processed MKV file: S01E04.processed.mkv
2025-09-17 16:08:48,584 - INFO - ⏳ Season 01 encoding in progress - 5 episodes remaining
2025-09-17 16:08:48,584 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:08:48,584 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E04
2025-09-17 16:08:48,585 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:08:48,585 - INFO - Processing episode (audio-only): Futurama (1999) S01E05
2025-09-17 16:08:48,585 - INFO - Original episode bitrate for Futurama (1999) S01E05: 5942.7 kbps (5.9 Mbps)
2025-09-17 16:08:48,585 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:08:48,585 - INFO - Episode encoding parameters for Futurama (1999) S01E05: 1080p @ 7.0 Mbps
2025-09-17 16:08:48,585 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05\S01E05.encoded.mkv
2025-09-17 16:08:48,586 - INFO - Extracting and transcoding audio: S01E05.processed.mkv
2025-09-17 16:09:15,282 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:09:15,282 - INFO - Muxing video with new audio: S01E05.encoded.mkv
2025-09-17 16:09:16,535 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:09:16,537 - INFO - Output file verification passed: 959.0 MB
2025-09-17 16:09:16,538 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:09:16,539 - INFO - 📍 Found matching audio file for S01E05: S01E05.aac
2025-09-17 16:09:16,543 - INFO - ✅ Copied audio file: S01E05.aac (20.6 MB)
2025-09-17 16:09:16,544 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:09:16,544 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05
2025-09-17 16:09:16,610 - INFO - 🗑️ Deleted processed MKV file: S01E05.processed.mkv
2025-09-17 16:09:16,610 - INFO - ⏳ Season 01 encoding in progress - 4 episodes remaining
2025-09-17 16:09:16,610 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:09:16,610 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E05
2025-09-17 16:09:16,611 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:09:16,611 - INFO - Processing episode (audio-only): Futurama (1999) S01E06
2025-09-17 16:09:16,611 - INFO - Original episode bitrate for Futurama (1999) S01E06: 6297.5 kbps (6.3 Mbps)
2025-09-17 16:09:16,611 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:09:16,611 - INFO - Episode encoding parameters for Futurama (1999) S01E06: 1080p @ 7.0 Mbps
2025-09-17 16:09:16,611 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06\S01E06.encoded.mkv
2025-09-17 16:09:16,612 - INFO - Extracting and transcoding audio: S01E06.processed.mkv
2025-09-17 16:09:46,862 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:09:46,863 - INFO - Muxing video with new audio: S01E06.encoded.mkv
2025-09-17 16:09:48,372 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:09:48,373 - INFO - Output file verification passed: 1015.3 MB
2025-09-17 16:09:48,453 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:09:48,453 - INFO - 📍 Found matching audio file for S01E06: S01E06.aac
2025-09-17 16:09:48,458 - INFO - ✅ Copied audio file: S01E06.aac (20.6 MB)
2025-09-17 16:09:48,458 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:09:48,458 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06
2025-09-17 16:09:48,533 - INFO - 🗑️ Deleted processed MKV file: S01E06.processed.mkv
2025-09-17 16:09:48,533 - INFO - ⏳ Season 01 encoding in progress - 3 episodes remaining
2025-09-17 16:09:48,533 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:09:48,534 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E06
2025-09-17 16:09:48,534 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:09:48,534 - INFO - Processing episode (audio-only): Futurama (1999) S01E07
2025-09-17 16:09:48,534 - INFO - Original episode bitrate for Futurama (1999) S01E07: 5510.3 kbps (5.5 Mbps)
2025-09-17 16:09:48,534 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:09:48,534 - INFO - Episode encoding parameters for Futurama (1999) S01E07: 1080p @ 7.0 Mbps
2025-09-17 16:09:48,535 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07\S01E07.encoded.mkv
2025-09-17 16:09:48,535 - INFO - Extracting and transcoding audio: S01E07.processed.mkv
2025-09-17 16:10:16,947 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:10:16,947 - INFO - Muxing video with new audio: S01E07.encoded.mkv
2025-09-17 16:10:18,276 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:10:18,278 - INFO - Output file verification passed: 888.9 MB
2025-09-17 16:10:18,279 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:10:18,280 - INFO - 📍 Found matching audio file for S01E07: S01E07.aac
2025-09-17 16:10:18,284 - INFO - ✅ Copied audio file: S01E07.aac (20.6 MB)
2025-09-17 16:10:18,284 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:10:18,285 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07
2025-09-17 16:10:18,347 - INFO - 🗑️ Deleted processed MKV file: S01E07.processed.mkv
2025-09-17 16:10:18,347 - INFO - ⏳ Season 01 encoding in progress - 2 episodes remaining
2025-09-17 16:10:18,347 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:10:18,347 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E07
2025-09-17 16:10:18,348 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:10:18,348 - INFO - Processing episode (audio-only): Futurama (1999) S01E08
2025-09-17 16:10:18,348 - INFO - Original episode bitrate for Futurama (1999) S01E08: 5988.9 kbps (6.0 Mbps)
2025-09-17 16:10:18,348 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:10:18,348 - INFO - Episode encoding parameters for Futurama (1999) S01E08: 1080p @ 7.0 Mbps
2025-09-17 16:10:18,348 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08\S01E08.encoded.mkv
2025-09-17 16:10:18,349 - INFO - Extracting and transcoding audio: S01E08.processed.mkv
2025-09-17 16:10:49,691 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:10:49,691 - INFO - Muxing video with new audio: S01E08.encoded.mkv
2025-09-17 16:10:50,775 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:10:50,777 - INFO - Output file verification passed: 966.4 MB
2025-09-17 16:10:50,778 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:10:50,778 - INFO - 📍 Found matching audio file for S01E08: S01E08.aac
2025-09-17 16:10:50,783 - INFO - ✅ Copied audio file: S01E08.aac (20.6 MB)
2025-09-17 16:10:50,783 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:10:50,783 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08
2025-09-17 16:10:50,869 - INFO - 🗑️ Deleted processed MKV file: S01E08.processed.mkv
2025-09-17 16:10:50,870 - INFO - ⏳ Season 01 encoding in progress - 1 episodes remaining
2025-09-17 16:10:50,870 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:10:50,870 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E08
2025-09-17 16:10:50,870 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:10:50,871 - INFO - Processing episode (audio-only): Futurama (1999) S01E09
2025-09-17 16:10:50,871 - INFO - Original episode bitrate for Futurama (1999) S01E09: 6502.8 kbps (6.5 Mbps)
2025-09-17 16:10:50,871 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:10:50,871 - INFO - Episode encoding parameters for Futurama (1999) S01E09: 1080p @ 7.0 Mbps
2025-09-17 16:10:50,871 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09\S01E09.encoded.mkv
2025-09-17 16:10:50,872 - INFO - Extracting and transcoding audio: S01E09.processed.mkv
2025-09-17 16:11:14,705 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:11:14,705 - INFO - Muxing video with new audio: S01E09.encoded.mkv
2025-09-17 16:11:15,917 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:11:15,919 - INFO - Output file verification passed: 1049.2 MB
2025-09-17 16:11:15,920 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:11:15,920 - INFO - 📍 Found matching audio file for S01E09: S01E09.aac
2025-09-17 16:11:15,925 - INFO - ✅ Copied audio file: S01E09.aac (20.6 MB)
2025-09-17 16:11:15,925 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:11:15,925 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09
2025-09-17 16:11:16,014 - INFO - 🗑️ Deleted processed MKV file: S01E09.processed.mkv
2025-09-17 16:11:16,014 - INFO - 🎯 Season 01 encoding complete - all episodes processed
2025-09-17 16:11:16,030 - INFO - 🗑️ Deleted season _Processed_Audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:11:16,030 - INFO - 🎯 Season 01 fully processed - all shared resources cleaned
2025-09-17 16:11:16,030 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E09
2025-09-17 16:11:16,030 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:17:51,758 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 16:17:51,759 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 16:17:51,760 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 16:17:51,760 - INFO - Filesystem-first video encoder initialized
2025-09-17 16:17:51,760 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 16:17:51,760 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 16:17:51,760 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 16:17:51,760 - INFO - Workspace: .
2025-09-17 16:17:53,253 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 16:17:53,280 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 16:17:53,303 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 16:17:53,325 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 16:17:53,348 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 16:17:53,369 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 16:17:53,391 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 16:17:53,414 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 16:17:53,437 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 16:17:53,458 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 16:17:53,459 - INFO - Found 9 episodes ready for encoding
2025-09-17 16:18:04,511 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 16:18:04,511 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5250.1 kbps (5.3 Mbps)
2025-09-17 16:18:04,511 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:18:04,511 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 16:18:04,513 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01\S01E01.encoded.mkv
2025-09-17 16:18:04,514 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 16:18:21,428 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:18:21,428 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 16:18:22,810 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:18:22,811 - INFO - Output file verification passed: 846.9 MB
2025-09-17 16:18:22,812 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:18:22,813 - INFO - 📍 Found matching audio file for S01E01: S01E01.aac
2025-09-17 16:18:22,819 - INFO - ✅ Copied audio file: S01E01.aac (20.6 MB)
2025-09-17 16:18:22,819 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:18:22,819 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01
2025-09-17 16:18:22,877 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 16:18:22,877 - INFO - ⏳ Season 01 encoding in progress - 8 episodes remaining
2025-09-17 16:18:22,877 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:18:22,877 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 16:18:22,878 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:18:22,878 - INFO - Processing episode (audio-only): Futurama (1999) S01E02
2025-09-17 16:18:22,878 - INFO - Original episode bitrate for Futurama (1999) S01E02: 5587.7 kbps (5.6 Mbps)
2025-09-17 16:18:22,879 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:18:22,879 - INFO - Episode encoding parameters for Futurama (1999) S01E02: 1080p @ 7.0 Mbps
2025-09-17 16:18:22,879 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02\S01E02.encoded.mkv
2025-09-17 16:18:22,879 - INFO - Extracting and transcoding audio: S01E02.processed.mkv
2025-09-17 16:18:41,771 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:18:41,771 - INFO - Muxing video with new audio: S01E02.encoded.mkv
2025-09-17 16:18:42,882 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:18:42,883 - INFO - Output file verification passed: 902.1 MB
2025-09-17 16:18:42,884 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:18:42,885 - INFO - 📍 Found matching audio file for S01E02: S01E02.aac
2025-09-17 16:18:42,889 - INFO - ✅ Copied audio file: S01E02.aac (20.6 MB)
2025-09-17 16:18:42,889 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:18:42,889 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02
2025-09-17 16:18:42,954 - INFO - 🗑️ Deleted processed MKV file: S01E02.processed.mkv
2025-09-17 16:18:42,954 - INFO - ⏳ Season 01 encoding in progress - 7 episodes remaining
2025-09-17 16:18:42,954 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:18:42,954 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E02
2025-09-17 16:18:42,955 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:18:42,955 - INFO - Processing episode (audio-only): Futurama (1999) S01E03
2025-09-17 16:18:42,955 - INFO - Original episode bitrate for Futurama (1999) S01E03: 5310.1 kbps (5.3 Mbps)
2025-09-17 16:18:42,955 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:18:42,955 - INFO - Episode encoding parameters for Futurama (1999) S01E03: 1080p @ 7.0 Mbps
2025-09-17 16:18:42,957 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03\S01E03.encoded.mkv
2025-09-17 16:18:42,957 - INFO - Extracting and transcoding audio: S01E03.processed.mkv
2025-09-17 16:19:06,366 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:19:06,366 - INFO - Muxing video with new audio: S01E03.encoded.mkv
2025-09-17 16:19:07,353 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:19:07,354 - INFO - Output file verification passed: 856.9 MB
2025-09-17 16:19:07,355 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:19:07,356 - INFO - 📍 Found matching audio file for S01E03: S01E03.aac
2025-09-17 16:19:07,362 - INFO - ✅ Copied audio file: S01E03.aac (20.6 MB)
2025-09-17 16:19:07,363 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:19:07,363 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03
2025-09-17 16:19:07,424 - INFO - 🗑️ Deleted processed MKV file: S01E03.processed.mkv
2025-09-17 16:19:07,424 - INFO - ⏳ Season 01 encoding in progress - 6 episodes remaining
2025-09-17 16:19:07,424 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:19:07,424 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E03
2025-09-17 16:19:07,424 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:19:07,425 - INFO - Processing episode (audio-only): Futurama (1999) S01E04
2025-09-17 16:19:07,425 - INFO - Original episode bitrate for Futurama (1999) S01E04: 5284.7 kbps (5.3 Mbps)
2025-09-17 16:19:07,425 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:19:07,425 - INFO - Episode encoding parameters for Futurama (1999) S01E04: 1080p @ 7.0 Mbps
2025-09-17 16:19:07,425 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04\S01E04.encoded.mkv
2025-09-17 16:19:07,426 - INFO - Extracting and transcoding audio: S01E04.processed.mkv
2025-09-17 16:19:31,137 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:19:31,138 - INFO - Muxing video with new audio: S01E04.encoded.mkv
2025-09-17 16:19:32,312 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:19:32,313 - INFO - Output file verification passed: 853.0 MB
2025-09-17 16:19:32,387 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:19:32,387 - INFO - 📍 Found matching audio file for S01E04: S01E04.aac
2025-09-17 16:19:32,392 - INFO - ✅ Copied audio file: S01E04.aac (20.6 MB)
2025-09-17 16:19:32,392 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:19:32,392 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04
2025-09-17 16:19:32,450 - INFO - 🗑️ Deleted processed MKV file: S01E04.processed.mkv
2025-09-17 16:19:32,451 - INFO - ⏳ Season 01 encoding in progress - 5 episodes remaining
2025-09-17 16:19:32,451 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:19:32,451 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E04
2025-09-17 16:19:32,451 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:19:32,451 - INFO - Processing episode (audio-only): Futurama (1999) S01E05
2025-09-17 16:19:32,452 - INFO - Original episode bitrate for Futurama (1999) S01E05: 5942.7 kbps (5.9 Mbps)
2025-09-17 16:19:32,452 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:19:32,452 - INFO - Episode encoding parameters for Futurama (1999) S01E05: 1080p @ 7.0 Mbps
2025-09-17 16:19:32,452 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05\S01E05.encoded.mkv
2025-09-17 16:19:32,453 - INFO - Extracting and transcoding audio: S01E05.processed.mkv
2025-09-17 16:19:54,168 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:19:54,168 - INFO - Muxing video with new audio: S01E05.encoded.mkv
2025-09-17 16:19:55,570 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:19:55,571 - INFO - Output file verification passed: 959.0 MB
2025-09-17 16:19:55,572 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:19:55,573 - INFO - 📍 Found matching audio file for S01E05: S01E05.aac
2025-09-17 16:19:55,578 - INFO - ✅ Copied audio file: S01E05.aac (20.6 MB)
2025-09-17 16:19:55,578 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:19:55,578 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05
2025-09-17 16:19:55,643 - INFO - 🗑️ Deleted processed MKV file: S01E05.processed.mkv
2025-09-17 16:19:55,644 - INFO - ⏳ Season 01 encoding in progress - 4 episodes remaining
2025-09-17 16:19:55,644 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:19:55,644 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E05
2025-09-17 16:19:55,644 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:19:55,645 - INFO - Processing episode (audio-only): Futurama (1999) S01E06
2025-09-17 16:19:55,645 - INFO - Original episode bitrate for Futurama (1999) S01E06: 6297.5 kbps (6.3 Mbps)
2025-09-17 16:19:55,645 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:19:55,646 - INFO - Episode encoding parameters for Futurama (1999) S01E06: 1080p @ 7.0 Mbps
2025-09-17 16:19:55,646 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06\S01E06.encoded.mkv
2025-09-17 16:19:55,646 - INFO - Extracting and transcoding audio: S01E06.processed.mkv
2025-09-17 16:20:20,450 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:20:20,451 - INFO - Muxing video with new audio: S01E06.encoded.mkv
2025-09-17 16:20:21,658 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:20:21,660 - INFO - Output file verification passed: 1015.3 MB
2025-09-17 16:20:21,737 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:20:21,737 - INFO - 📍 Found matching audio file for S01E06: S01E06.aac
2025-09-17 16:20:21,744 - INFO - ✅ Copied audio file: S01E06.aac (20.6 MB)
2025-09-17 16:20:21,744 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:20:21,744 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06
2025-09-17 16:20:21,816 - INFO - 🗑️ Deleted processed MKV file: S01E06.processed.mkv
2025-09-17 16:20:21,816 - INFO - ⏳ Season 01 encoding in progress - 3 episodes remaining
2025-09-17 16:20:21,816 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:20:21,816 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E06
2025-09-17 16:20:21,817 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:20:21,817 - INFO - Processing episode (audio-only): Futurama (1999) S01E07
2025-09-17 16:20:21,817 - INFO - Original episode bitrate for Futurama (1999) S01E07: 5510.3 kbps (5.5 Mbps)
2025-09-17 16:20:21,817 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:20:21,817 - INFO - Episode encoding parameters for Futurama (1999) S01E07: 1080p @ 7.0 Mbps
2025-09-17 16:20:21,817 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07\S01E07.encoded.mkv
2025-09-17 16:20:21,818 - INFO - Extracting and transcoding audio: S01E07.processed.mkv
2025-09-17 16:20:45,479 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:20:45,479 - INFO - Muxing video with new audio: S01E07.encoded.mkv
2025-09-17 16:20:46,893 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:20:46,895 - INFO - Output file verification passed: 888.9 MB
2025-09-17 16:20:46,896 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:20:46,896 - INFO - 📍 Found matching audio file for S01E07: S01E07.aac
2025-09-17 16:20:46,901 - INFO - ✅ Copied audio file: S01E07.aac (20.6 MB)
2025-09-17 16:20:46,901 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:20:46,901 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07
2025-09-17 16:20:46,962 - INFO - 🗑️ Deleted processed MKV file: S01E07.processed.mkv
2025-09-17 16:20:46,963 - INFO - ⏳ Season 01 encoding in progress - 2 episodes remaining
2025-09-17 16:20:46,963 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:20:46,963 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E07
2025-09-17 16:20:46,963 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:20:46,964 - INFO - Processing episode (audio-only): Futurama (1999) S01E08
2025-09-17 16:20:46,964 - INFO - Original episode bitrate for Futurama (1999) S01E08: 5988.9 kbps (6.0 Mbps)
2025-09-17 16:20:46,964 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:20:46,964 - INFO - Episode encoding parameters for Futurama (1999) S01E08: 1080p @ 7.0 Mbps
2025-09-17 16:20:46,964 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08\S01E08.encoded.mkv
2025-09-17 16:20:46,964 - INFO - Extracting and transcoding audio: S01E08.processed.mkv
2025-09-17 16:21:12,235 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:21:12,235 - INFO - Muxing video with new audio: S01E08.encoded.mkv
2025-09-17 16:21:13,472 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:21:13,474 - INFO - Output file verification passed: 966.4 MB
2025-09-17 16:21:13,475 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:21:13,475 - INFO - 📍 Found matching audio file for S01E08: S01E08.aac
2025-09-17 16:21:13,481 - INFO - ✅ Copied audio file: S01E08.aac (20.6 MB)
2025-09-17 16:21:13,481 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:21:13,481 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08
2025-09-17 16:21:13,552 - INFO - 🗑️ Deleted processed MKV file: S01E08.processed.mkv
2025-09-17 16:21:13,552 - INFO - ⏳ Season 01 encoding in progress - 1 episodes remaining
2025-09-17 16:21:13,552 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:21:13,552 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E08
2025-09-17 16:21:13,553 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:21:13,553 - INFO - Processing episode (audio-only): Futurama (1999) S01E09
2025-09-17 16:21:13,553 - INFO - Original episode bitrate for Futurama (1999) S01E09: 6502.8 kbps (6.5 Mbps)
2025-09-17 16:21:13,553 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:21:13,553 - INFO - Episode encoding parameters for Futurama (1999) S01E09: 1080p @ 7.0 Mbps
2025-09-17 16:21:13,554 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09\S01E09.encoded.mkv
2025-09-17 16:21:13,554 - INFO - Extracting and transcoding audio: S01E09.processed.mkv
2025-09-17 16:21:33,166 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:21:33,166 - INFO - Muxing video with new audio: S01E09.encoded.mkv
2025-09-17 16:21:34,392 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:21:34,394 - INFO - Output file verification passed: 1049.2 MB
2025-09-17 16:21:34,395 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:21:34,395 - INFO - 📍 Found matching audio file for S01E09: S01E09.aac
2025-09-17 16:21:34,400 - INFO - ✅ Copied audio file: S01E09.aac (20.6 MB)
2025-09-17 16:21:34,400 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:21:34,400 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09
2025-09-17 16:21:34,471 - INFO - 🗑️ Deleted processed MKV file: S01E09.processed.mkv
2025-09-17 16:21:34,471 - INFO - 🎯 Season 01 encoding complete - all episodes processed
2025-09-17 16:21:34,486 - INFO - 🗑️ Deleted season _Processed_Audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:21:34,486 - INFO - 🎯 Season 01 fully processed - all shared resources cleaned
2025-09-17 16:21:34,486 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E09
2025-09-17 16:21:34,486 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:24:12,084 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 16:24:12,084 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 16:24:12,085 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 16:24:12,085 - INFO - Filesystem-first video encoder initialized
2025-09-17 16:24:12,086 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 16:24:12,086 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 16:24:12,086 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 16:24:12,086 - INFO - Workspace: .
2025-09-17 16:24:13,457 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 16:24:13,487 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 16:24:13,509 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 16:24:13,530 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 16:24:13,553 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 16:24:13,574 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 16:24:13,597 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 16:24:13,620 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 16:24:13,643 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 16:24:13,664 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 16:24:13,665 - INFO - Found 9 episodes ready for encoding
2025-09-17 16:24:24,634 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 16:24:24,634 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5250.1 kbps (5.3 Mbps)
2025-09-17 16:24:24,634 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:24:24,634 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 16:24:24,636 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01\S01E01.encoded.mkv
2025-09-17 16:24:24,640 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 16:24:45,106 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:24:45,107 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 16:24:46,397 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:24:46,398 - INFO - Output file verification passed: 846.9 MB
2025-09-17 16:24:46,399 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:24:46,400 - INFO - 📍 Found matching audio file for S01E01: S01E01.aac
2025-09-17 16:24:46,404 - INFO - ✅ Copied audio file: S01E01.aac (20.6 MB)
2025-09-17 16:24:46,404 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:24:46,404 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01
2025-09-17 16:24:46,487 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 16:24:46,488 - INFO - ⏳ Season 01 encoding in progress - 8 episodes remaining
2025-09-17 16:24:46,488 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:24:46,488 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 16:24:46,488 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:24:46,489 - INFO - Processing episode (audio-only): Futurama (1999) S01E02
2025-09-17 16:24:46,489 - INFO - Original episode bitrate for Futurama (1999) S01E02: 5587.7 kbps (5.6 Mbps)
2025-09-17 16:24:46,489 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:24:46,489 - INFO - Episode encoding parameters for Futurama (1999) S01E02: 1080p @ 7.0 Mbps
2025-09-17 16:24:46,489 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02\S01E02.encoded.mkv
2025-09-17 16:24:46,490 - INFO - Extracting and transcoding audio: S01E02.processed.mkv
2025-09-17 16:25:08,270 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:25:08,270 - INFO - Muxing video with new audio: S01E02.encoded.mkv
2025-09-17 16:25:09,370 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:25:09,372 - INFO - Output file verification passed: 902.1 MB
2025-09-17 16:25:09,373 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:25:09,374 - INFO - 📍 Found matching audio file for S01E02: S01E02.aac
2025-09-17 16:25:09,379 - INFO - ✅ Copied audio file: S01E02.aac (20.6 MB)
2025-09-17 16:25:09,379 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:25:09,379 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02
2025-09-17 16:25:09,439 - INFO - 🗑️ Deleted processed MKV file: S01E02.processed.mkv
2025-09-17 16:25:09,439 - INFO - ⏳ Season 01 encoding in progress - 7 episodes remaining
2025-09-17 16:25:09,439 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:25:09,439 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E02
2025-09-17 16:25:09,440 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:25:09,440 - INFO - Processing episode (audio-only): Futurama (1999) S01E03
2025-09-17 16:25:09,440 - INFO - Original episode bitrate for Futurama (1999) S01E03: 5310.1 kbps (5.3 Mbps)
2025-09-17 16:25:09,440 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:25:09,440 - INFO - Episode encoding parameters for Futurama (1999) S01E03: 1080p @ 7.0 Mbps
2025-09-17 16:25:09,441 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03\S01E03.encoded.mkv
2025-09-17 16:25:09,442 - INFO - Extracting and transcoding audio: S01E03.processed.mkv
2025-09-17 16:25:38,686 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:25:38,686 - INFO - Muxing video with new audio: S01E03.encoded.mkv
2025-09-17 16:25:39,899 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:25:39,901 - INFO - Output file verification passed: 856.9 MB
2025-09-17 16:25:39,903 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:25:39,903 - INFO - 📍 Found matching audio file for S01E03: S01E03.aac
2025-09-17 16:25:39,910 - INFO - ✅ Copied audio file: S01E03.aac (20.6 MB)
2025-09-17 16:25:39,910 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:25:39,910 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03
2025-09-17 16:25:39,969 - INFO - 🗑️ Deleted processed MKV file: S01E03.processed.mkv
2025-09-17 16:25:39,969 - INFO - ⏳ Season 01 encoding in progress - 6 episodes remaining
2025-09-17 16:25:39,969 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:25:39,969 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E03
2025-09-17 16:25:39,969 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:25:39,969 - INFO - Processing episode (audio-only): Futurama (1999) S01E04
2025-09-17 16:25:39,969 - INFO - Original episode bitrate for Futurama (1999) S01E04: 5284.7 kbps (5.3 Mbps)
2025-09-17 16:25:39,969 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:25:39,969 - INFO - Episode encoding parameters for Futurama (1999) S01E04: 1080p @ 7.0 Mbps
2025-09-17 16:25:39,970 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04\S01E04.encoded.mkv
2025-09-17 16:25:39,970 - INFO - Extracting and transcoding audio: S01E04.processed.mkv
2025-09-17 16:26:09,194 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:26:09,194 - INFO - Muxing video with new audio: S01E04.encoded.mkv
2025-09-17 16:26:10,388 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:26:10,390 - INFO - Output file verification passed: 853.0 MB
2025-09-17 16:26:10,493 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:26:10,495 - INFO - 📍 Found matching audio file for S01E04: S01E04.aac
2025-09-17 16:26:10,504 - INFO - ✅ Copied audio file: S01E04.aac (20.6 MB)
2025-09-17 16:26:10,504 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:26:10,504 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04
2025-09-17 16:26:10,563 - INFO - 🗑️ Deleted processed MKV file: S01E04.processed.mkv
2025-09-17 16:26:10,564 - INFO - ⏳ Season 01 encoding in progress - 5 episodes remaining
2025-09-17 16:26:10,564 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:26:10,564 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E04
2025-09-17 16:26:10,564 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:26:10,564 - INFO - Processing episode (audio-only): Futurama (1999) S01E05
2025-09-17 16:26:10,564 - INFO - Original episode bitrate for Futurama (1999) S01E05: 5942.7 kbps (5.9 Mbps)
2025-09-17 16:26:10,564 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:26:10,565 - INFO - Episode encoding parameters for Futurama (1999) S01E05: 1080p @ 7.0 Mbps
2025-09-17 16:26:10,565 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05\S01E05.encoded.mkv
2025-09-17 16:26:10,565 - INFO - Extracting and transcoding audio: S01E05.processed.mkv
2025-09-17 16:26:38,139 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:26:38,139 - INFO - Muxing video with new audio: S01E05.encoded.mkv
2025-09-17 16:26:39,444 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:26:39,447 - INFO - Output file verification passed: 959.0 MB
2025-09-17 16:26:39,448 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:26:39,448 - INFO - 📍 Found matching audio file for S01E05: S01E05.aac
2025-09-17 16:26:39,453 - INFO - ✅ Copied audio file: S01E05.aac (20.6 MB)
2025-09-17 16:26:39,453 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:26:39,453 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05
2025-09-17 16:26:39,515 - INFO - 🗑️ Deleted processed MKV file: S01E05.processed.mkv
2025-09-17 16:26:39,515 - INFO - ⏳ Season 01 encoding in progress - 4 episodes remaining
2025-09-17 16:26:39,516 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:26:39,516 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E05
2025-09-17 16:26:39,516 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:26:39,516 - INFO - Processing episode (audio-only): Futurama (1999) S01E06
2025-09-17 16:26:39,516 - INFO - Original episode bitrate for Futurama (1999) S01E06: 6297.5 kbps (6.3 Mbps)
2025-09-17 16:26:39,516 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:26:39,516 - INFO - Episode encoding parameters for Futurama (1999) S01E06: 1080p @ 7.0 Mbps
2025-09-17 16:26:39,517 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06\S01E06.encoded.mkv
2025-09-17 16:26:39,517 - INFO - Extracting and transcoding audio: S01E06.processed.mkv
2025-09-17 16:27:09,575 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:27:09,575 - INFO - Muxing video with new audio: S01E06.encoded.mkv
2025-09-17 16:27:11,055 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:27:11,057 - INFO - Output file verification passed: 1015.3 MB
2025-09-17 16:27:11,059 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:27:11,060 - INFO - 📍 Found matching audio file for S01E06: S01E06.aac
2025-09-17 16:27:11,066 - INFO - ✅ Copied audio file: S01E06.aac (20.6 MB)
2025-09-17 16:27:11,066 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:27:11,066 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06
2025-09-17 16:27:11,165 - INFO - 🗑️ Deleted processed MKV file: S01E06.processed.mkv
2025-09-17 16:27:11,165 - INFO - ⏳ Season 01 encoding in progress - 3 episodes remaining
2025-09-17 16:27:11,165 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:27:11,165 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E06
2025-09-17 16:27:11,166 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:27:11,166 - INFO - Processing episode (audio-only): Futurama (1999) S01E07
2025-09-17 16:27:11,166 - INFO - Original episode bitrate for Futurama (1999) S01E07: 5510.3 kbps (5.5 Mbps)
2025-09-17 16:27:11,166 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:27:11,166 - INFO - Episode encoding parameters for Futurama (1999) S01E07: 1080p @ 7.0 Mbps
2025-09-17 16:27:11,167 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07\S01E07.encoded.mkv
2025-09-17 16:27:11,167 - INFO - Extracting and transcoding audio: S01E07.processed.mkv
2025-09-17 16:27:40,433 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:27:40,433 - INFO - Muxing video with new audio: S01E07.encoded.mkv
2025-09-17 16:27:41,690 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:27:41,691 - INFO - Output file verification passed: 888.9 MB
2025-09-17 16:27:41,692 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:27:41,692 - INFO - 📍 Found matching audio file for S01E07: S01E07.aac
2025-09-17 16:27:41,699 - INFO - ✅ Copied audio file: S01E07.aac (20.6 MB)
2025-09-17 16:27:41,699 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:27:41,699 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07
2025-09-17 16:27:41,771 - INFO - 🗑️ Deleted processed MKV file: S01E07.processed.mkv
2025-09-17 16:27:41,771 - INFO - ⏳ Season 01 encoding in progress - 2 episodes remaining
2025-09-17 16:27:41,771 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:27:41,771 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E07
2025-09-17 16:27:41,772 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:27:41,772 - INFO - Processing episode (audio-only): Futurama (1999) S01E08
2025-09-17 16:27:41,772 - INFO - Original episode bitrate for Futurama (1999) S01E08: 5988.9 kbps (6.0 Mbps)
2025-09-17 16:27:41,772 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:27:41,772 - INFO - Episode encoding parameters for Futurama (1999) S01E08: 1080p @ 7.0 Mbps
2025-09-17 16:27:41,772 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08\S01E08.encoded.mkv
2025-09-17 16:27:41,773 - INFO - Extracting and transcoding audio: S01E08.processed.mkv
2025-09-17 16:28:12,499 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:28:12,499 - INFO - Muxing video with new audio: S01E08.encoded.mkv
2025-09-17 16:28:13,615 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:28:13,618 - INFO - Output file verification passed: 966.4 MB
2025-09-17 16:28:13,619 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:28:13,619 - INFO - 📍 Found matching audio file for S01E08: S01E08.aac
2025-09-17 16:28:13,624 - INFO - ✅ Copied audio file: S01E08.aac (20.6 MB)
2025-09-17 16:28:13,624 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:28:13,624 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08
2025-09-17 16:28:13,689 - INFO - 🗑️ Deleted processed MKV file: S01E08.processed.mkv
2025-09-17 16:28:13,690 - INFO - ⏳ Season 01 encoding in progress - 1 episodes remaining
2025-09-17 16:28:13,690 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 16:28:13,690 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E08
2025-09-17 16:28:13,690 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 16:28:13,690 - INFO - Processing episode (audio-only): Futurama (1999) S01E09
2025-09-17 16:28:13,690 - INFO - Original episode bitrate for Futurama (1999) S01E09: 6502.8 kbps (6.5 Mbps)
2025-09-17 16:28:13,690 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 16:28:13,690 - INFO - Episode encoding parameters for Futurama (1999) S01E09: 1080p @ 7.0 Mbps
2025-09-17 16:28:13,690 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09\S01E09.encoded.mkv
2025-09-17 16:28:13,691 - INFO - Extracting and transcoding audio: S01E09.processed.mkv
2025-09-17 16:28:36,559 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 16:28:36,559 - INFO - Muxing video with new audio: S01E09.encoded.mkv
2025-09-17 16:28:37,715 - INFO - ✅ Video/audio muxing successful
2025-09-17 16:28:37,718 - INFO - Output file verification passed: 1049.2 MB
2025-09-17 16:28:37,771 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:28:37,772 - INFO - 📍 Found matching audio file for S01E09: S01E09.aac
2025-09-17 16:28:37,777 - INFO - ✅ Copied audio file: S01E09.aac (20.6 MB)
2025-09-17 16:28:37,777 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:28:37,777 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09
2025-09-17 16:28:37,853 - INFO - 🗑️ Deleted processed MKV file: S01E09.processed.mkv
2025-09-17 16:28:37,854 - INFO - 🎯 Season 01 encoding complete - all episodes processed
2025-09-17 16:28:37,868 - INFO - 🗑️ Deleted season _Processed_Audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 16:28:37,868 - INFO - 🎯 Season 01 fully processed - all shared resources cleaned
2025-09-17 16:28:37,868 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E09
2025-09-17 16:28:37,868 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:51:08,810 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 17:51:08,848 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 17:51:08,850 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 17:51:08,850 - INFO - Filesystem-first video encoder initialized
2025-09-17 17:51:08,850 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 17:51:08,850 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 17:51:08,850 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 17:51:08,850 - INFO - Workspace: .
2025-09-17 17:51:10,435 - INFO - Discovering movies ready for video encoding...
2025-09-17 17:51:10,780 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 17:51:10,780 - INFO - Found 1 movies ready for encoding
2025-09-17 17:51:10,781 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 17:51:10,803 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 17:51:10,828 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 17:51:10,851 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 17:51:10,874 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 17:51:10,895 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 17:51:10,917 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 17:51:10,940 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 17:51:10,962 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 17:51:10,983 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 17:51:10,983 - INFO - Found 9 episodes ready for encoding
2025-09-17 17:51:52,782 - INFO - Initialized metadata database at: _internal\data\movie_metadata.db
2025-09-17 17:51:52,782 - INFO - Loaded video encoder configuration from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\video_encoder_config.json
2025-09-17 17:51:52,784 - INFO - Loaded external tool paths from C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
2025-09-17 17:51:52,784 - INFO - Filesystem-first video encoder initialized
2025-09-17 17:51:52,784 - INFO - HandBrakeCLI path: C:\Users\<USER>\Videos\PlexAutomator\_internal\tools\HandBrakeCLI.exe
2025-09-17 17:51:52,784 - INFO - FFmpeg path: C:\ffmpeg\bin\ffmpeg.exe
2025-09-17 17:51:52,784 - INFO - MKVMerge path: C:\Program Files\MKVToolNix\mkvmerge.exe
2025-09-17 17:51:52,784 - INFO - Workspace: .
2025-09-17 17:51:54,842 - INFO - Discovering movies ready for video encoding...
2025-09-17 17:51:54,884 - INFO - Got actual duration from ffprobe: 132.7 seconds (2.2 minutes)
2025-09-17 17:51:54,884 - INFO - Found 1 movies ready for encoding
2025-09-17 17:51:54,885 - INFO - Discovering TV episodes ready for video encoding...
2025-09-17 17:51:54,905 - INFO - Got actual duration from ffprobe: 1351.4 seconds (22.5 minutes)
2025-09-17 17:51:54,926 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 17:51:54,946 - INFO - Got actual duration from ffprobe: 1352.8 seconds (22.5 minutes)
2025-09-17 17:51:54,967 - INFO - Got actual duration from ffprobe: 1353.1 seconds (22.6 minutes)
2025-09-17 17:51:54,987 - INFO - Got actual duration from ffprobe: 1353.0 seconds (22.5 minutes)
2025-09-17 17:51:55,010 - INFO - Got actual duration from ffprobe: 1351.7 seconds (22.5 minutes)
2025-09-17 17:51:55,031 - INFO - Got actual duration from ffprobe: 1352.3 seconds (22.5 minutes)
2025-09-17 17:51:55,053 - INFO - Got actual duration from ffprobe: 1352.6 seconds (22.5 minutes)
2025-09-17 17:51:55,073 - INFO - Got actual duration from ffprobe: 1352.5 seconds (22.5 minutes)
2025-09-17 17:51:55,073 - INFO - Found 9 episodes ready for encoding
2025-09-17 17:52:13,110 - INFO - Processing movie (audio-only): 13 Going on 30 (2004)
2025-09-17 17:52:13,110 - INFO - Original bitrate for 13 Going on 30: 28934.8 kbps (28.9 Mbps)
2025-09-17 17:52:13,110 - INFO - Compressed to 50.0% of original: 14.5 Mbps
2025-09-17 17:52:13,114 - INFO - Output path: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).encoded.mkv
2025-09-17 17:52:13,116 - INFO - Extracting and transcoding audio: 13 Going on 30 (2004).processed.mkv
2025-09-17 17:52:15,232 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:52:15,233 - INFO - Muxing video with new audio: 13 Going on 30 (2004).encoded.mkv
2025-09-17 17:52:15,914 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:52:15,915 - INFO - Output file verification passed: 437.1 MB
2025-09-17 17:52:15,916 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 17:52:15,925 - INFO - ✅ Copied largest audio file: TrueHD_5.1_eng_A_TRUEHD.thd (22.1 MB)
2025-09-17 17:52:15,926 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 17:52:15,926 - INFO -    To: workspace\4_ready_for_final_mux\movies\1080p\13 Going on 30 (2004)
2025-09-17 17:52:15,962 - INFO - 🗑️ Deleted processed MKV file: 13 Going on 30 (2004).processed.mkv
2025-09-17 17:52:15,966 - INFO - 🗑️ Deleted processed audio folder: workspace\3_mkv_cleaned_subtitles_extracted\movies\1080p\13 Going on 30 (2004)\_Processed_Audio
2025-09-17 17:52:15,966 - INFO - ✅ Stage 3 cleanup completed for 13 Going on 30
2025-09-17 17:52:15,971 - INFO - Saved encoding metadata for 13 Going on 30
2025-09-17 17:52:15,971 - INFO - ✅ Audio-only processing completed for 13 Going on 30
2025-09-17 17:52:15,972 - INFO - Processing episode (audio-only): Futurama (1999) S01E01
2025-09-17 17:52:15,972 - INFO - Original episode bitrate for Futurama (1999) S01E01: 5250.1 kbps (5.3 Mbps)
2025-09-17 17:52:15,972 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:52:15,972 - INFO - Episode encoding parameters for Futurama (1999) S01E01: 1080p @ 7.0 Mbps
2025-09-17 17:52:15,972 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01\S01E01.encoded.mkv
2025-09-17 17:52:15,973 - INFO - Extracting and transcoding audio: S01E01.processed.mkv
2025-09-17 17:52:36,023 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:52:36,023 - INFO - Muxing video with new audio: S01E01.encoded.mkv
2025-09-17 17:52:37,075 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:52:37,076 - INFO - Output file verification passed: 846.9 MB
2025-09-17 17:52:37,077 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:52:37,077 - INFO - 📍 Found matching audio file for S01E01: S01E01.aac
2025-09-17 17:52:37,083 - INFO - ✅ Copied audio file: S01E01.aac (20.6 MB)
2025-09-17 17:52:37,083 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:52:37,083 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 01
2025-09-17 17:52:37,144 - INFO - 🗑️ Deleted processed MKV file: S01E01.processed.mkv
2025-09-17 17:52:37,144 - INFO - ⏳ Season 01 encoding in progress - 8 episodes remaining
2025-09-17 17:52:37,144 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:52:37,144 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E01
2025-09-17 17:52:37,145 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:52:37,145 - INFO - Processing episode (audio-only): Futurama (1999) S01E02
2025-09-17 17:52:37,146 - INFO - Original episode bitrate for Futurama (1999) S01E02: 5587.7 kbps (5.6 Mbps)
2025-09-17 17:52:37,146 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:52:37,146 - INFO - Episode encoding parameters for Futurama (1999) S01E02: 1080p @ 7.0 Mbps
2025-09-17 17:52:37,146 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02\S01E02.encoded.mkv
2025-09-17 17:52:37,146 - INFO - Extracting and transcoding audio: S01E02.processed.mkv
2025-09-17 17:52:58,637 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:52:58,637 - INFO - Muxing video with new audio: S01E02.encoded.mkv
2025-09-17 17:52:59,580 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:52:59,582 - INFO - Output file verification passed: 902.1 MB
2025-09-17 17:52:59,583 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:52:59,583 - INFO - 📍 Found matching audio file for S01E02: S01E02.aac
2025-09-17 17:52:59,598 - INFO - ✅ Copied audio file: S01E02.aac (20.6 MB)
2025-09-17 17:52:59,598 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:52:59,599 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 02
2025-09-17 17:52:59,674 - INFO - 🗑️ Deleted processed MKV file: S01E02.processed.mkv
2025-09-17 17:52:59,674 - INFO - ⏳ Season 01 encoding in progress - 7 episodes remaining
2025-09-17 17:52:59,675 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:52:59,675 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E02
2025-09-17 17:52:59,675 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:52:59,675 - INFO - Processing episode (audio-only): Futurama (1999) S01E03
2025-09-17 17:52:59,675 - INFO - Original episode bitrate for Futurama (1999) S01E03: 5310.1 kbps (5.3 Mbps)
2025-09-17 17:52:59,675 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:52:59,675 - INFO - Episode encoding parameters for Futurama (1999) S01E03: 1080p @ 7.0 Mbps
2025-09-17 17:52:59,677 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03\S01E03.encoded.mkv
2025-09-17 17:52:59,677 - INFO - Extracting and transcoding audio: S01E03.processed.mkv
2025-09-17 17:53:27,921 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:53:27,921 - INFO - Muxing video with new audio: S01E03.encoded.mkv
2025-09-17 17:53:28,950 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:53:28,952 - INFO - Output file verification passed: 856.9 MB
2025-09-17 17:53:28,953 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:53:28,954 - INFO - 📍 Found matching audio file for S01E03: S01E03.aac
2025-09-17 17:53:28,963 - INFO - ✅ Copied audio file: S01E03.aac (20.6 MB)
2025-09-17 17:53:28,963 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:53:28,963 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 03
2025-09-17 17:53:29,024 - INFO - 🗑️ Deleted processed MKV file: S01E03.processed.mkv
2025-09-17 17:53:29,025 - INFO - ⏳ Season 01 encoding in progress - 6 episodes remaining
2025-09-17 17:53:29,025 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:53:29,025 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E03
2025-09-17 17:53:29,025 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:53:29,025 - INFO - Processing episode (audio-only): Futurama (1999) S01E04
2025-09-17 17:53:29,026 - INFO - Original episode bitrate for Futurama (1999) S01E04: 5284.7 kbps (5.3 Mbps)
2025-09-17 17:53:29,026 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:53:29,026 - INFO - Episode encoding parameters for Futurama (1999) S01E04: 1080p @ 7.0 Mbps
2025-09-17 17:53:29,026 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04\S01E04.encoded.mkv
2025-09-17 17:53:29,026 - INFO - Extracting and transcoding audio: S01E04.processed.mkv
2025-09-17 17:53:58,090 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:53:58,090 - INFO - Muxing video with new audio: S01E04.encoded.mkv
2025-09-17 17:53:59,080 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:53:59,081 - INFO - Output file verification passed: 853.0 MB
2025-09-17 17:53:59,088 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:53:59,089 - INFO - 📍 Found matching audio file for S01E04: S01E04.aac
2025-09-17 17:53:59,095 - INFO - ✅ Copied audio file: S01E04.aac (20.6 MB)
2025-09-17 17:53:59,095 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:53:59,095 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 04
2025-09-17 17:53:59,188 - INFO - 🗑️ Deleted processed MKV file: S01E04.processed.mkv
2025-09-17 17:53:59,189 - INFO - ⏳ Season 01 encoding in progress - 5 episodes remaining
2025-09-17 17:53:59,189 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:53:59,189 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E04
2025-09-17 17:53:59,189 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:53:59,189 - INFO - Processing episode (audio-only): Futurama (1999) S01E05
2025-09-17 17:53:59,190 - INFO - Original episode bitrate for Futurama (1999) S01E05: 5942.7 kbps (5.9 Mbps)
2025-09-17 17:53:59,190 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:53:59,190 - INFO - Episode encoding parameters for Futurama (1999) S01E05: 1080p @ 7.0 Mbps
2025-09-17 17:53:59,191 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05\S01E05.encoded.mkv
2025-09-17 17:53:59,191 - INFO - Extracting and transcoding audio: S01E05.processed.mkv
2025-09-17 17:54:25,576 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:54:25,576 - INFO - Muxing video with new audio: S01E05.encoded.mkv
2025-09-17 17:54:26,651 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:54:26,652 - INFO - Output file verification passed: 959.0 MB
2025-09-17 17:54:26,655 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:54:26,655 - INFO - 📍 Found matching audio file for S01E05: S01E05.aac
2025-09-17 17:54:26,660 - INFO - ✅ Copied audio file: S01E05.aac (20.6 MB)
2025-09-17 17:54:26,660 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:54:26,660 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 05
2025-09-17 17:54:26,737 - INFO - 🗑️ Deleted processed MKV file: S01E05.processed.mkv
2025-09-17 17:54:26,738 - INFO - ⏳ Season 01 encoding in progress - 4 episodes remaining
2025-09-17 17:54:26,738 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:54:26,738 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E05
2025-09-17 17:54:26,738 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:54:26,738 - INFO - Processing episode (audio-only): Futurama (1999) S01E06
2025-09-17 17:54:26,738 - INFO - Original episode bitrate for Futurama (1999) S01E06: 6297.5 kbps (6.3 Mbps)
2025-09-17 17:54:26,738 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:54:26,739 - INFO - Episode encoding parameters for Futurama (1999) S01E06: 1080p @ 7.0 Mbps
2025-09-17 17:54:26,739 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06\S01E06.encoded.mkv
2025-09-17 17:54:26,739 - INFO - Extracting and transcoding audio: S01E06.processed.mkv
2025-09-17 17:54:56,873 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:54:56,873 - INFO - Muxing video with new audio: S01E06.encoded.mkv
2025-09-17 17:54:57,954 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:54:57,957 - INFO - Output file verification passed: 1015.3 MB
2025-09-17 17:54:58,015 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:54:58,016 - INFO - 📍 Found matching audio file for S01E06: S01E06.aac
2025-09-17 17:54:58,024 - INFO - ✅ Copied audio file: S01E06.aac (20.6 MB)
2025-09-17 17:54:58,024 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:54:58,024 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 06
2025-09-17 17:54:58,111 - INFO - 🗑️ Deleted processed MKV file: S01E06.processed.mkv
2025-09-17 17:54:58,111 - INFO - ⏳ Season 01 encoding in progress - 3 episodes remaining
2025-09-17 17:54:58,112 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:54:58,112 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E06
2025-09-17 17:54:58,112 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:54:58,112 - INFO - Processing episode (audio-only): Futurama (1999) S01E07
2025-09-17 17:54:58,112 - INFO - Original episode bitrate for Futurama (1999) S01E07: 5510.3 kbps (5.5 Mbps)
2025-09-17 17:54:58,112 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:54:58,112 - INFO - Episode encoding parameters for Futurama (1999) S01E07: 1080p @ 7.0 Mbps
2025-09-17 17:54:58,113 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07\S01E07.encoded.mkv
2025-09-17 17:54:58,113 - INFO - Extracting and transcoding audio: S01E07.processed.mkv
2025-09-17 17:55:26,412 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:55:26,412 - INFO - Muxing video with new audio: S01E07.encoded.mkv
2025-09-17 17:55:27,570 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:55:27,572 - INFO - Output file verification passed: 888.9 MB
2025-09-17 17:55:27,573 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:55:27,574 - INFO - 📍 Found matching audio file for S01E07: S01E07.aac
2025-09-17 17:55:27,583 - INFO - ✅ Copied audio file: S01E07.aac (20.6 MB)
2025-09-17 17:55:27,583 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:55:27,583 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 07
2025-09-17 17:55:27,686 - INFO - 🗑️ Deleted processed MKV file: S01E07.processed.mkv
2025-09-17 17:55:27,687 - INFO - ⏳ Season 01 encoding in progress - 2 episodes remaining
2025-09-17 17:55:27,688 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:55:27,688 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E07
2025-09-17 17:55:27,688 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:55:27,689 - INFO - Processing episode (audio-only): Futurama (1999) S01E08
2025-09-17 17:55:27,689 - INFO - Original episode bitrate for Futurama (1999) S01E08: 5988.9 kbps (6.0 Mbps)
2025-09-17 17:55:27,690 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:55:27,690 - INFO - Episode encoding parameters for Futurama (1999) S01E08: 1080p @ 7.0 Mbps
2025-09-17 17:55:27,690 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08\S01E08.encoded.mkv
2025-09-17 17:55:27,691 - INFO - Extracting and transcoding audio: S01E08.processed.mkv
2025-09-17 17:55:58,228 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:55:58,228 - INFO - Muxing video with new audio: S01E08.encoded.mkv
2025-09-17 17:55:59,336 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:55:59,338 - INFO - Output file verification passed: 966.4 MB
2025-09-17 17:55:59,339 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:55:59,339 - INFO - 📍 Found matching audio file for S01E08: S01E08.aac
2025-09-17 17:55:59,343 - INFO - ✅ Copied audio file: S01E08.aac (20.6 MB)
2025-09-17 17:55:59,343 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:55:59,344 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 08
2025-09-17 17:55:59,423 - INFO - 🗑️ Deleted processed MKV file: S01E08.processed.mkv
2025-09-17 17:55:59,423 - INFO - ⏳ Season 01 encoding in progress - 1 episodes remaining
2025-09-17 17:55:59,423 - INFO - ⏳ Keeping _Processed_Audio folder - other episodes in season still need processing
2025-09-17 17:55:59,423 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E08
2025-09-17 17:55:59,424 - INFO - ✅ Audio-only processing completed for episode
2025-09-17 17:55:59,424 - INFO - Processing episode (audio-only): Futurama (1999) S01E09
2025-09-17 17:55:59,424 - INFO - Original episode bitrate for Futurama (1999) S01E09: 6502.8 kbps (6.5 Mbps)
2025-09-17 17:55:59,424 - INFO - Original below episode minimum, using floor: 7.0 Mbps
2025-09-17 17:55:59,424 - INFO - Episode encoding parameters for Futurama (1999) S01E09: 1080p @ 7.0 Mbps
2025-09-17 17:55:59,425 - INFO - Output path: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09\S01E09.encoded.mkv
2025-09-17 17:55:59,429 - INFO - Extracting and transcoding audio: S01E09.processed.mkv
2025-09-17 17:56:22,487 - INFO - ✅ Audio extraction/transcoding successful
2025-09-17 17:56:22,488 - INFO - Muxing video with new audio: S01E09.encoded.mkv
2025-09-17 17:56:23,730 - INFO - ✅ Video/audio muxing successful
2025-09-17 17:56:23,732 - INFO - Output file verification passed: 1049.2 MB
2025-09-17 17:56:23,733 - INFO - 🔍 Looking for audio files in: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:56:23,733 - INFO - 📍 Found matching audio file for S01E09: S01E09.aac
2025-09-17 17:56:23,738 - INFO - ✅ Copied audio file: S01E09.aac (20.6 MB)
2025-09-17 17:56:23,738 - INFO -    From: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:56:23,738 - INFO -    To: workspace\4_ready_for_final_mux\tv_shows\1080p\Futurama (1999)\Season 01\Episode 09
2025-09-17 17:56:23,837 - INFO - 🗑️ Deleted processed MKV file: S01E09.processed.mkv
2025-09-17 17:56:23,838 - INFO - 🎯 Season 01 encoding complete - all episodes processed
2025-09-17 17:56:23,859 - INFO - 🗑️ Deleted season _Processed_Audio folder: workspace\3_mkv_cleaned_subtitles_extracted\tv_shows\1080p\Futurama (1999)\Season 01\_Processed_Audio
2025-09-17 17:56:23,860 - INFO - 🎯 Season 01 fully processed - all shared resources cleaned
2025-09-17 17:56:23,860 - INFO - ✅ Stage 3 cleanup completed for Futurama (1999) S01E09
2025-09-17 17:56:23,860 - INFO - ✅ Audio-only processing completed for episode
