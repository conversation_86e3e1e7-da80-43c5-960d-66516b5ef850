# Temp Original Backup Infrastructure Challenge

## System Overview

PlexAutomator processes movies and TV shows through a multi-stage pipeline with temporary backup management for poster processing and quality control.

### Script Responsibilities

#### Script 3 (MKV Processor)
- **Function**: Processes video files and creates backups of original content
- **Backup Creation**: Uses `fs_manager.move_original_to_backup(source_dir, resolution)`
- **Structure Created**: 
  ```
  temp_original_backup/
  ├── movies/[resolution]/MovieName/
  └── tv_shows/[resolution]/ShowName/
  ```
- **Resolution Detection**: Analyzes video files to determine 4k, 1080p, 720p, 480p
- **Key Behavior**: Creates resolution-specific backup folders for both movies and TV shows

#### Script 7 (<PERSON><PERSON>)
- **Function**: Downloads and processes posters from multiple sources (TMDB, Fanart.tv, ThePosterDB)
- **Input Source**: Processes content from `5_awaiting_poster/`
- **Output Target**: Needs to place posters in `temp_original_backup/[content_type]/[resolution]/posters/`
- **Current Challenge**: Must determine correct resolution folder for poster placement

#### Scripts 8-9 (Quality Check & Deployment)
- **Function**: Visual quality verification and final Plex deployment
- **Dependency**: Relies on temp_original_backup structure for poster and content organization

### Current Folder Structure States

#### Folder 5 (Awaiting Poster) - Script 7 Input
```
5_awaiting_poster/
├── movies/
│   ├── 4k/MovieName/
│   └── 1080p/MovieName/
└── tv_shows/
    └── ShowName/  ← NO RESOLUTION ORGANIZATION (consolidated)
```

#### Temp Original Backup - Script 7 Output Target
```
temp_original_backup/
├── movies/
│   ├── 4k/
│   │   ├── MovieName/           ← Script 3 backup
│   │   ├── posters/MovieName/   ← Script 7 target
│   │   └── temp_analysis/       ← Script 7 working files
│   └── 1080p/
│       ├── MovieName/
│       ├── posters/MovieName/
│       └── temp_analysis/
└── tv_shows/
    ├── 4k/
    │   ├── ShowName/            ← Script 3 backup
    │   ├── posters/ShowName/    ← Script 7 target
    │   └── temp_analysis/
    ├── 1080p/
    │   ├── ShowName/
    │   ├── posters/ShowName/
    │   └── temp_analysis/
    └── 720p/
        ├── ShowName/
        ├── posters/ShowName/
        └── temp_analysis/
```

## The Core Problem: TV Show Resolution Detection

### Problem Statement
Script 7 processes TV shows from the consolidated structure `5_awaiting_poster/tv_shows/ShowName/` but must place posters in resolution-specific folders within temp_original_backup. The challenge is determining the correct resolution folder when multiple seasons of the same show exist at different resolutions.

### Complex Scenario Example
```
# Script 3 has created multiple resolution backups for the same show:
temp_original_backup/tv_shows/
├── 720p/Futurama (1999)/Season 01/
├── 1080p/Futurama (1999)/Season 02/
├── 1080p/Futurama (1999)/Season 03/
└── 4k/Futurama (1999)/Season 04/

# Script 7 processes from consolidated structure:
5_awaiting_poster/tv_shows/Futurama (1999)/
├── Season 01/
├── Season 02/
├── Season 03/
└── Season 04/

# Question: Which resolution folder should receive the posters?
# temp_original_backup/tv_shows/720p/posters/Futurama (1999)/
# temp_original_backup/tv_shows/1080p/posters/Futurama (1999)/
# temp_original_backup/tv_shows/4k/posters/Futurama (1999)/
```

### Current Movie Logic (Works Well)
```python
# For movies, Script 7 can detect resolution from folder structure:
movie_path = Path("5_awaiting_poster/movies/4k/MovieName")
detected_resolution = movie_path.parent.name  # "4k"
poster_target = f"temp_original_backup/movies/{detected_resolution}/posters/MovieName/"
```

### TV Show Challenge (Needs Solution)
```python
# For TV shows, no resolution info in folder structure:
tv_path = Path("5_awaiting_poster/tv_shows/ShowName")
detected_resolution = "???"  # Cannot determine from path
# Multiple possible targets exist in temp_original_backup
```

## Solution Requirements

### Must Satisfy:
1. **Consistency**: Same show must use same resolution folder for all poster operations
2. **Accuracy**: Poster placement should logically correspond to content resolution
3. **Reliability**: Solution must work for edge cases (mixed resolutions, single episodes)
4. **Performance**: Should not require extensive file system scanning
5. **Maintainability**: Other scripts should easily understand the logic

### Constraints:
1. **Folder 5 Structure**: TV shows remain consolidated (user requirement)
2. **Script 3 Behavior**: Creates resolution-specific backups (cannot change)
3. **Multi-Resolution Reality**: Same show can legitimately exist at multiple resolutions
4. **Processing Context**: Script 7 may not know what resolution it's currently processing

## Potential Solution Strategies

### Strategy 1: Dominant Resolution Placement
- **Logic**: Place posters in resolution folder containing the most seasons/episodes
- **Pros**: Statistically most representative
- **Cons**: May not reflect current processing resolution

### Strategy 2: Highest Resolution Priority
- **Logic**: Always use highest resolution folder found for the show
- **Pros**: Ensures best quality poster placement
- **Cons**: May not match actual content being processed

### Strategy 3: Multiple Poster Distribution
- **Logic**: Copy posters to ALL resolution folders where show exists
- **Pros**: Ensures availability regardless of resolution
- **Cons**: Storage duplication, complexity in cleanup

### Strategy 4: Metadata-Driven Detection
- **Logic**: Analyze video files in folder 5 to determine resolution
- **Pros**: Accurate to current content
- **Cons**: Performance impact, requires video analysis

### Strategy 5: Processing Context Awareness
- **Logic**: Modify Script 7 to receive resolution context from calling script
- **Pros**: Most accurate approach
- **Cons**: Requires architectural changes

### Strategy 6: Latest Activity Heuristic
- **Logic**: Use resolution folder with most recent backup activity
- **Pros**: Likely represents current processing batch
- **Cons**: Requires timestamp analysis

## Implementation Considerations

### Data Available to Script 7:
1. Show name and structure in folder 5
2. Existing backup folders in temp_original_backup
3. File modification timestamps
4. Video file metadata (if analyzed)

### Performance Requirements:
- Solution should not significantly slow poster processing
- Minimize file system operations
- Cache resolution decisions when possible

### Error Handling:
- Graceful fallback when no backups exist
- Handle partially processed shows
- Manage conflicting resolution information

## Request for Research

Please analyze these strategies and recommend the optimal approach considering:

1. **System Architecture**: How does this fit with the existing pipeline?
2. **Edge Case Handling**: What happens with incomplete data or mixed resolutions?
3. **Future Scalability**: How will this handle growing content libraries?
4. **Maintenance Burden**: Which approach is easiest to debug and modify?
5. **User Experience**: Which strategy provides the most predictable behavior?

Additionally, consider if there are alternative architectural approaches that could better solve this fundamental challenge while maintaining the existing script responsibilities and folder structure requirements.