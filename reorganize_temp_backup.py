#!/usr/bin/env python3
"""
Phase 2: Reorganize temp_original_backup structure
Transform from flat structure to resolution-based organization
"""

import shutil
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def reorganize_temp_backup():
    """
    Reorganize temp_original_backup structure:
    
    From:
    temp_original_backup/
        movies/1080p/MovieName/
        tv_shows/1080p/ShowName/
        Posters/MovieOrShowName/
        temp_analysis/files...
    
    To:
    temp_original_backup/
        movies/1080p/MovieName/
        movies/1080p/posters/MovieName/
        movies/1080p/temp_analysis/files...
        tv_shows/1080p/ShowName/
        tv_shows/1080p/posters/ShowName/
        tv_shows/1080p/temp_analysis/files...
    """
    base_path = Path("workspace") / "temp_original_backup"
    
    if not base_path.exists():
        logger.info("🔍 No temp_original_backup folder found - nothing to reorganize")
        return
    
    logger.info("📁 Starting Phase 2 reorganization of temp_original_backup structure")
    
    # Get current structure
    posters_dir = base_path / "Posters"
    temp_analysis_dir = base_path / "temp_analysis"
    
    # Find all existing resolutions for movies and TV shows
    movies_resolutions = []
    tv_resolutions = []
    
    movies_dir = base_path / "movies"
    tv_shows_dir = base_path / "tv_shows"
    
    if movies_dir.exists():
        movies_resolutions = [d.name for d in movies_dir.iterdir() if d.is_dir()]
        logger.info(f"📽️ Found movie resolutions: {movies_resolutions}")
    
    if tv_shows_dir.exists():
        tv_resolutions = [d.name for d in tv_shows_dir.iterdir() if d.is_dir()]
        logger.info(f"📺 Found TV show resolutions: {tv_resolutions}")
    
    # Add any missing standard resolutions
    all_movie_resolutions = set(movies_resolutions + ["4k", "1080p"])
    all_tv_resolutions = set(tv_resolutions + ["4k", "1080p", "720p", "480p"])
    
    # Create resolution-based poster and temp_analysis directories
    logger.info("🏗️ Creating new resolution-based directory structure")
    
    # Create movie resolution directories
    for resolution in all_movie_resolutions:
        res_path = movies_dir / resolution
        res_path.mkdir(parents=True, exist_ok=True)
        
        posters_path = res_path / "posters"
        temp_analysis_path = res_path / "temp_analysis"
        posters_path.mkdir(exist_ok=True)
        temp_analysis_path.mkdir(exist_ok=True)
        logger.info(f"✅ Created movie/{resolution}/posters/ and movie/{resolution}/temp_analysis/")
    
    # Create TV show resolution directories
    for resolution in all_tv_resolutions:
        res_path = tv_shows_dir / resolution
        res_path.mkdir(parents=True, exist_ok=True)
        
        posters_path = res_path / "posters"
        temp_analysis_path = res_path / "temp_analysis"
        posters_path.mkdir(exist_ok=True)
        temp_analysis_path.mkdir(exist_ok=True)
        logger.info(f"✅ Created tv_shows/{resolution}/posters/ and tv_shows/{resolution}/temp_analysis/")
    
    # Migrate existing Posters content
    if posters_dir.exists():
        logger.info("📂 Migrating existing poster content")
        
        for poster_item in posters_dir.iterdir():
            if poster_item.is_dir():
                poster_name = poster_item.name
                logger.info(f"🖼️ Processing poster folder: {poster_name}")
                
                # Determine if this is a movie or TV show based on naming patterns
                # TV shows often have year in format "Show Name (Year)"
                # Check existing backups to categorize correctly
                is_tv_show = False
                is_movie = False
                
                # Check if this poster exists in any TV show backup folders
                for tv_res in tv_resolutions:
                    tv_backup_path = tv_shows_dir / tv_res
                    if tv_backup_path.exists():
                        for show_backup in tv_backup_path.iterdir():
                            if show_backup.is_dir() and show_backup.name == poster_name:
                                is_tv_show = True
                                break
                    if is_tv_show:
                        break
                
                # Check if this poster exists in any movie backup folders
                if not is_tv_show:
                    for movie_res in movies_resolutions:
                        movie_backup_path = movies_dir / movie_res
                        if movie_backup_path.exists():
                            for movie_backup in movie_backup_path.iterdir():
                                if movie_backup.is_dir() and movie_backup.name == poster_name:
                                    is_movie = True
                                    break
                        if is_movie:
                            break
                
                # Default to movie if not found in backups (safer assumption)
                if not is_tv_show and not is_movie:
                    is_movie = True
                    logger.info(f"⚠️ Could not determine type for {poster_name}, defaulting to movie")
                
                # Move to appropriate resolution folder (default to 1080p)
                target_resolution = "1080p"
                
                if is_tv_show:
                    target_path = tv_shows_dir / target_resolution / "posters" / poster_name
                    logger.info(f"📺 Moving TV show poster to tv_shows/{target_resolution}/posters/{poster_name}")
                else:
                    target_path = movies_dir / target_resolution / "posters" / poster_name
                    logger.info(f"📽️ Moving movie poster to movies/{target_resolution}/posters/{poster_name}")
                
                if target_path.exists():
                    logger.info(f"🔄 Removing existing target: {target_path}")
                    shutil.rmtree(target_path)
                
                shutil.move(str(poster_item), str(target_path))
                logger.info(f"✅ Moved {poster_name} poster")
    
    # Migrate existing temp_analysis content
    if temp_analysis_dir.exists():
        logger.info("📁 Migrating existing temp_analysis content")
        
        # Move all temp_analysis files to movies/1080p/temp_analysis/ as default
        # (Scripts will create resolution-specific content going forward)
        target_temp_analysis = movies_dir / "1080p" / "temp_analysis"
        
        for temp_item in temp_analysis_dir.iterdir():
            target_path = target_temp_analysis / temp_item.name
            
            if target_path.exists():
                if target_path.is_dir():
                    shutil.rmtree(target_path)
                else:
                    target_path.unlink()
            
            shutil.move(str(temp_item), str(target_path))
            logger.info(f"✅ Moved temp_analysis item: {temp_item.name}")
    
    # Remove old flat directories
    if posters_dir.exists():
        shutil.rmtree(posters_dir)
        logger.info("🗑️ Removed old Posters/ directory")
    
    if temp_analysis_dir.exists():
        shutil.rmtree(temp_analysis_dir)
        logger.info("🗑️ Removed old temp_analysis/ directory")
    
    logger.info("🎉 Phase 2 reorganization completed successfully!")
    logger.info("📁 New structure:")
    logger.info("   temp_original_backup/")
    logger.info("   ├── movies/")
    for res in sorted(all_movie_resolutions):
        logger.info(f"   │   ├── {res}/")
        logger.info(f"   │   │   ├── posters/")
        logger.info(f"   │   │   └── temp_analysis/")
    logger.info("   └── tv_shows/")
    for res in sorted(all_tv_resolutions):
        logger.info(f"       ├── {res}/")
        logger.info(f"       │   ├── posters/")
        logger.info(f"       │   └── temp_analysis/")

if __name__ == "__main__":
    reorganize_temp_backup()