# Research Questions - Answers for ChatGPT Analysis

## Question 1: Preferred Behavior - Poster Location Strategy

**Answer: One definitive resolution folder per show (NO duplication)**

**Reasoning:**
- User explicitly stated: "we just need one for that show... we're not going to have multiple folders with posters for that one show"
- Storage efficiency: Avoid unnecessary duplication
- Maintenance simplicity: Single source of truth for each show's posters
- Logical consistency: Each show should have one primary resolution designation

**Recommendation for ChatGPT:** Research the best heuristic for selecting the single "definitive" resolution folder (highest quality, most seasons, most recent activity, etc.)

## Question 2: Performance Sensitivity - Video Metadata Scanning

**Answer: File scanning acceptable if needed, but folder-based detection preferred**

**Reasoning:**
- User preference: "I guess it's easier to do it more scanning" but prefers folder-based detection when possible
- Performance consideration: Folder scanning is faster than video analysis
- Reliability concern: "if a resolution folder is deleted, then how is it going to do its job?"
- Fallback strategy: Use metadata scanning as backup when folder heuristics fail

**Recommendation for ChatGPT:** Prioritize folder-based detection methods, but include metadata scanning as a reliable fallback option.

## Question 3: Script Interaction - Script 7 ↔ Script 3 Communication

**Answer: YES - Script communication is encouraged**

**Reasoning:**
- User explicitly supports: "I think this is a good idea. I think it's cool to have script 7 and script 3 communicate"
- Architectural benefit: Direct communication provides most accurate resolution context
- Implementation options: Marker files, JSON metadata, shared state, or direct parameter passing
- Maintains script integrity: Can enhance without breaking existing functionality

**Recommendation for ChatGPT:** Explore communication mechanisms that allow Script 3 to pass resolution context to Script 7 during the pipeline flow.

## Question 4: Storage Constraints - Disk Usage Concerns

**Answer: NO duplication - Storage efficiency required**

**Reasoning:**
- User strongly against duplication: "as long as they're not storing the exact same copies, within multiple resolutions... that's stupid"
- Single poster set per show: "we just need one for that show... whatever one was produced"
- Practical constraint: Large poster collections would waste significant disk space
- Maintenance burden: Multiple copies create cleanup and sync complications

**Recommendation for ChatGPT:** Design solution that stores exactly one poster set per show in the most appropriate resolution folder.

## Additional Context for ChatGPT Research

### User's Flexibility on Architecture:
- **Open to new ideas:** "I'm okay with new ideas" for infrastructure improvements
- **Constraint:** Don't break existing Script 3 and Script 7 core functionality
- **Goal:** Better architecture for temp_original_backup that handles resolution mismatches

### Current Pain Points:
1. **Script 3:** Creates resolution-based backups (has resolution info)
2. **Script 7:** Processes consolidated TV shows (lacks resolution info)
3. **Challenge:** Bridge the information gap without major architectural disruption

### Success Criteria:
- Single poster location per show (no duplication)
- Reliable resolution detection/assignment
- Maintain existing script workflows
- Handle edge cases (mixed resolutions, missing data)
- Performance-acceptable solution

## Request for ChatGPT:
Based on these answers, please research and recommend the optimal architecture that:
1. Selects one definitive resolution folder per TV show for poster storage
2. Uses folder-based detection when possible, metadata scanning as fallback
3. Leverages Script 3 ↔ Script 7 communication for resolution context
4. Eliminates storage duplication while maintaining reliability
5. Suggests specific implementation approaches for the recommended strategy